# AGENTS.md

This file provides guidance to agents when working with code in this repository.

- Build (Windows only): run [`build.bat`](build.bat:1). It requires `ffmpeg.exe`, `AkVCamManager.exe` and `akvirtualcamera-windows-9.1.2.exe` present in repo root; the script calls PyInstaller with --add-binary to embed them; output `virtual_camera_fratar.exe`.
- Runtime resource loading: [`virtual_camera.py`](virtual_camera.py:10) uses sys._MEIPASS when bundled — when running from source ensure `AkVCamManager.exe` is present in working dir or resource_path will fail.
- Logging: logs are written to tempfile.gettempdir()/camera_virtual_fratar.log by [`logging_setup.py`](logging_setup.py:6); check system Temp folder when debugging.
- Single-instance: mutex name is "Global\\VirtualCameraFratarMutex" in [`single_instance.py`](single_instance.py:1); starting a second instance will exit.
- AkVCam commands: device id must be `CameraFratar1`; creation uses exact commands in [`virtual_camera.py`](virtual_camera.py:69) including format `YUY2 1280 720 16`. Changing these strings breaks device creation.
- get_dump_info() requires that `AkVCamManager.exe dump` returns valid XML; [`virtual_camera.py`](virtual_camera.py:44) does ET.fromstring(xml) — invalid/empty response will raise.
- Installer: [`installer.py`](installer.py:11) uses `winget` and downloads binaries to %TEMP% and %APPDATA% Startup; requires network and elevated rights.
- Streaming stop semantics: stop logic searches process names `ffmpeg.exe` and `akvcammanager.exe` (lowercase) via psutil; ensure external processes match those names.
- No test framework or linter configs found; there is no single-test command—there are no tests to run.
- Required Python deps (install manually): pystray, Pillow, psutil, pywin32. The project assumes running on Windows.

Every line above is project-specific and was discovered in the repo files (build.bat, virtual_camera.py, logging_setup.py, installer.py). Do not add generic framework rules.

Utilize MCPs sempre que possível para garantir melhor qualidade na obtenção de documentação de apis e bibliotecas (usando context7) e ou buscando informações adicionais e atualizadas na internet.