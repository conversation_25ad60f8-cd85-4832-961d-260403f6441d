# Instalação e Validação do camera-virtual-fratar

## Resumo executivo
- O instalador atualmente automa­tiza: instalação do FFmpeg via winget, instalação do AKVirtualCamera (akvirtualcamera-windows-9.1.2.exe), cópia/execução do binário do app (`virtual_camera_fratar.exe`) para a pasta Startup.
- Objetivo desta atualização: descrever um fluxo 100% offline e autossuficiente (onefile exe) que permite instalar tudo sem Internet, sem winget e sem Python no cliente, usando apenas os binários empacotados.
- Referências principais do repositório: [`build.bat`](build.bat:1), [`installer.py`](installer.py:1), [`camera_virtual_fratar/virtual_camera.py`](camera_virtual_fratar/virtual_camera.py:1), [`camera_virtual_fratar/logging_setup.py`](camera_virtual_fratar/logging_setup.py:1), [`camera_virtual_fratar/single_instance.py`](camera_virtual_fratar/single_instance.py:1).

## Instalação 100% offline e autossuficiente
Visão geral
- É viável empacotar tudo em um único executável Windows gerado por PyInstaller (onefile) de forma que, ao executar `virtual_camera_fratar.exe --install`, o binário internalize o runtime Python e os binários nativos necessários e realize a instalação local sem acesso à Internet.
- Requisitos: o builder (quem gera o .exe) precisa providenciar e embutir todos os binários necessários (ver lista abaixo). O instalador no cliente deverá ser executado com privilégios administrativos (UAC) para instalação do driver.

Artefatos que devem ser empacotados (mínimo recomendado)
- `ffmpeg.exe` (usado para streaming/fallback)
- `ffprobe.exe` (recomendado para probing; atualmente presente na raiz do repo como [`ffprobe.exe`](ffprobe.exe:1) — recomenda-se adicioná-lo ao bundle)
- `AkVCamManager.exe` (ferramenta de gerenciamento do driver; usada por [`get_dump_info()`](camera_virtual_fratar/virtual_camera.py:44) e demais chamadas)
- `AkVCamManager.exe` (ferramenta/serviço usada para instalar o driver programaticamente — conforme wiki "Build and install" do AKVirtualCamera)
- `akvirtualcamera-windows-9.1.2.exe` (instalador offline do driver, fallback para cenários onde o assistant não esteja disponível)

Observações importantes
- A instalação do driver requer privilégios administrativos (UAC). O instalador deve elevar o processo quando necessário.
- O runtime Python fica empacotado no exe gerado por PyInstaller onefile — assim o cliente não precisa ter Python instalado.

### Como o comando --install deve operar (proposta operacional)
Proposta de fluxo do comando `virtual_camera_fratar.exe --install` (comportamento sugerido a implementar/seguir):

1. Elevação de privilégio
   - Ao detectar que não está em modo elevado, solicitar UAC e relançar o processo como Administrador. A instalação do driver requer privilégios administrativos (UAC).

2. Instalação do driver AKVirtualCamera (fluxo preferencial)
   - Opção A (preferida, quando suportado - CONFIRMADO pela wiki do projeto):
     - Executar `AkVCamManager.exe --install` (usar a versão x64/x86 conforme plataforma) para realizar a instalação do driver/serviço, e em seguida iniciar o serviço com:
       - `sc start AkVCamManager`
     - Esta sequência está documentada na wiki "Build and install" do AKVirtualCamera (ver seção "Referências oficiais (confirmadas)").
     - Implementação: chamar o executável empacotado via `resource_path()` e executar o comando elevando quando necessário (ex.: subprocess com caminho retornado por `resource_path("AkVCamManager.exe")`).
   - Opção B (fallback - NÃO CONFIRMADO; testar):
     - Executar o instalador nativo `akvirtualcamera-windows-9.1.2.exe` em modo silencioso. Switches silenciosos dependem do framework do instalador (ver seção "Gaps de informação que precisam de confirmação"). Exemplos de tentativa para testar:
       - NSIS: `akvirtualcamera-windows-9.1.2.exe /S`
       - Inno Setup: `akvirtualcamera-windows-9.1.2.exe /VERYSILENT /NORESTART`
     - Registrar saída e falhas; quando possível preferir o fluxo do `AkVCamManager.exe` por ser o método exposto pela documentação oficial.

   - Em todos os casos:
     - Logar saída e erros; abortar claramente se a instalação falhar.
     - Reforçar: executar em prompt elevado (UAC) — sem privilégios administrativos a instalação do driver provavelmente falhará.

3. Criação/configuração do device virtual
   - Executar a sequência de comandos documentada em [`create_virtual_camera()`](camera_virtual_fratar/virtual_camera.py:152):
     - `add-device -i CameraFratar1 "Virtual Camera Fratar 1"`
     - `add-format CameraFratar1 YUY2 1280 720 16`
     - `update`
   - Validar o resultado via `AkVCamManager.exe dump` e parse com [`get_dump_info()`](camera_virtual_fratar/virtual_camera.py:44).
   - Os comandos são enviados via [`run_akvcam_command()`](camera_virtual_fratar/virtual_camera.py:70).

4. Deploy do binário e autostart
   - Copiar o próprio `virtual_camera_fratar.exe` para a pasta Startup do usuário: `%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\virtual_camera_fratar.exe`
   - Opcional avançado: considerar instalação All Users (ProgramData/Start Menu) e/ou criação de tarefa no Task Scheduler para execução à inicialização em cenários corporativos.

5. Logging
   - Garantir escrita de logs em `%TEMP%\camera_virtual_fratar.log` conforme [`logging_setup.setup_logging()`](camera_virtual_fratar/logging_setup.py:16).

6. Resultado esperado
   - `CameraFratar1` criado e utilizável.
   - Logs e artefatos gerados (ver seção "Verificações pós-instalação").

## Passo a passo para instalação no cliente (modo offline)
- Pré-requisito: ter o arquivo `virtual_camera_fratar.exe` gerado com todos os binários embutidos (ver seção de build).
- Comando único (executar em prompt elevado / PowerShell Admin):
  - `virtual_camera_fratar.exe --install`
- O comando `--install` deve:
  - Elevar para administrador (UAC) quando necessário.
  - Instalar driver (preferir `AkVCamManager.exe --install` e iniciar o serviço com `sc start AkVCamManager`, conforme wiki “Build and install”; fallback para instalador embutido).
  - Criar o device com os comandos previstos em [`create_virtual_camera()`](camera_virtual_fratar/virtual_camera.py:152).
  - Copiar o exe para Startup e inicializar quando indicado.
- Verificações offline pós-instalação:
  - `AkVCamManager.exe devices` — deve listar “CameraFratar1”.
  - `AkVCamManager.exe formats CameraFratar1` — deve conter “YUY2 1280 720 16”.
  - Logs em `%TEMP%\camera_virtual_fratar.log` (ver [`camera_virtual_fratar/logging_setup.py`](camera_virtual_fratar/logging_setup.py:16)).
- Observação sobre runtime empacotado:
  - Durante execução empacotada, recursos nativos são resolvidos via `resource_path()` que usa `sys._MEIPASS` quando gerado por PyInstaller (ver [`camera_virtual_fratar/virtual_camera.py`](camera_virtual_fratar/virtual_camera.py:13)). Isso significa que `AkVCamManager.exe` e outros binários estarão acessíveis a partir do bundle quando corretamente incluídos com `--add-binary`.

## Recomendações de build para 100% offline
- Incluir todos os binários necessários no PyInstaller onefile:
  - As inclusões já documentadas em [`build.bat`](build.bat:1) (veja `--add-binary` existentes em [`build.bat`](build.bat:49), [`build.bat`](build.bat:50), [`build.bat`](build.bat:51)).
  - Recomendação explícita: adicionar `ffprobe.exe` e `AkVCamManager.exe` ao bundle. Exemplo de linhas a incluir/ajustar em [`build.bat`](build.bat:49–51) (exemplo a adaptar conforme sintaxe do script):
    - --add-binary "%CD%\ffmpeg.exe;."
    - --add-binary "%CD%\ffprobe.exe;."
    - --add-binary "%CD%\AkVCamManager.exe;."
    - --add-binary "%CD%\AkVCamManager.exe;."
    - --add-binary "%CD%\akvirtualcamera-windows-9.1.2.exe;."
  - Documente essas alterações no processo de build/CI para garantir que os binários sejam embalados nas próximas builds.
- Assinatura de código
  - Assinar o executável (`virtual_camera_fratar.exe`) e, se aplicável, o driver/instalador do AKVirtualCamera reduz prompts do SmartScreen e facilita deploy corporativo.
- Remover o caminho de dependência ao `installer.py` no fluxo padrão
  - Recomenda-se manter [`installer.py`](installer.py:1) apenas como ferramenta legacy/auxiliar para ambientes que ainda dependem de downloads online; o fluxo principal de distribuição deve ser o onefile offline.
- Criar pipeline de CI que valide:
  - Presença de binários embutidos (verificar entradas `--add-binary`).
  - Smoke tests locais (gerar exe, executar `--help`, corrida básica sem instalar drivers).
  - Produção de artefatos assinados (quando disponível).

## Verificações pós-instalação (offline) — detalhado
- Logs
  - `%TEMP%\camera_virtual_fratar.log` — entradas de inicialização e erros (via [`camera_virtual_fratar/logging_setup.py`](camera_virtual_fratar/logging_setup.py:16)).
- Arquivos auxiliares
  - `last_device_format.json` — gravado pelo app com o formato ativo (ex.: `{"pix_fmt":"YUY2","width":1280,"height":720,"fps":16}`).
  - `camera_virtual_fratar_debug.log` — exemplo/arquivo auxiliar presente no repositório (pode aparecer em logs locais conforme debug).
- Comandos de validação offline (local onde `AkVCamManager.exe` está acessível — no contexto empacotado, invocar via `resource_path()`):
  - `AkVCamManager.exe devices` — listar devices.
  - `AkVCamManager.exe formats CameraFratar1` — verificar formatos.
  - `AkVCamManager.exe clients` — listar aplicações/consumidores conectados.
  - `AkVCamManager.exe dump` — validar XML (usado por [`get_dump_info()`](camera_virtual_fratar/virtual_camera.py:44)).
  - `AkVCamManager.exe update` — aplicar mudanças pendentes no grafo de dispositivos/formats.
- Validação do driver (programática): usar [`verify_camera_driver()`](camera_virtual_fratar/virtual_camera.py:174).

## Troubleshooting (offline) — pontos e ações
- Driver não instala
  - Verificar execução como Administrador (UAC).
  - Método preferido (CONFIRMADO pela documentação do projeto): executar `AkVCamManager.exe --install` (usar a versão x64/x86 correta) e, em seguida, `sc start AkVCamManager`. Esta abordagem é preferida porque replicará o fluxo documentado em "Build and install".
  - Se o assistant não estiver disponível ou o comando não funcionar no ambiente do cliente, usar o instalador embutido `akvirtualcamera-windows-9.1.2.exe` em modo silencioso (flags a confirmar — ver seção "Gaps de informação" abaixo).
  - Sempre capturar stdout/stderr e registrar em logs para diagnóstico.
- Device não aparece após instalar
  - Recriar via [`ensure_device_for_format()`](camera_virtual_fratar/virtual_camera.py:341) ou executar manualmente:
    - `AkVCamManager.exe add-device -i CameraFratar1 "Virtual Camera Fratar 1"`
    - `AkVCamManager.exe add-format CameraFratar1 YUY2 1280 720 16`
    - `AkVCamManager.exe update`
  - Verificar `AkVCamManager.exe dump` e checar XML bem-formado; usar [`get_dump_info()`](camera_virtual_fratar/virtual_camera.py:44) para parsing programático.
  - Se alterações de driver não surtirem efeito imediatamente, considerar reinício (ver "Gaps" — testar se o installer exige reboot).
- ffprobe ausente / probing falha
  - Aplicação tem fallback para `ffmpeg` (heurística em [`camera_virtual_fratar/video_probe.py`](camera_virtual_fratar/video_probe.py:186)), mas o empacotamento de `ffprobe.exe` reduz falhas de detecção — recomenda-se adicionar `ffprobe.exe` ao bundle.
- UAC / Permissões
  - Reexecutar `virtual_camera_fratar.exe --install` em prompt elevado.
- SmartScreen / bloqueios por políticas
  - Assinar código (recomendado) e, para testes, desbloquear manualmente o executável via propriedades/Windows Defender.
- Verificações de startup / deploy
  - Pastas de Startup:
    - Per-user: `%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup`
    - All Users: `%ProgramData%\Microsoft\Windows\Start Menu\Programs\Startup`
  - Em cenários avançados (All Users / políticas), considerar Task Scheduler ou instalação em ProgramData para garantir execução automática.
- Logs úteis para diagnóstico
  - `%TEMP%\camera_virtual_fratar.log`
  - Saída padrão/erro do `AkVCamManager.exe` (capturada pelo instalador/assistant).

## Gaps de informação que precisam de confirmação
- Switches / flags silenciosas do instalador `akvirtualcamera-windows-9.1.2.exe` — NÃO CONFIRMADO (testar):
  - Possíveis flags a tentar (teste prático em VM):
    - NSIS: `akvirtualcamera-windows-9.1.2.exe /S`
    - Inno Setup: `akvirtualcamera-windows-9.1.2.exe /VERYSILENT /NORESTART`
  - A ação recomendada é testar o instalador em uma VM Windows (x86/x64) e confirmar qual framework ele usa (NSIS, Inno, MSI) e quais switches funcionam.
- Uso de `AkVCamManager.exe --install` seguido de `sc start AkVCamManager` — CONFIRMADO (documentado na wiki "Build and install"):
  - Implementar esse fluxo como Opção A preferida; documentar que é o método oficial recomendado no repositório upstream.
- Se `AkVCamManager.exe install` instala o driver completo sem o instalador externo — NÃO CONFIRMADO (testar):
  - Há chamadas ao `install`/`uninstall` em [`virtual_camera.py`](camera_virtual_fratar/virtual_camera.py:292–298) (ex.: `run_akvcam_command(["install"])`), mas a documentação oficial prioriza o uso do `AkVCamManager.exe` para instalação; validar empiricamente se `AkVCamManager.exe install` sozinho é suficiente.
- Necessidade de reinicialização do sistema após instalação do driver — NÃO CONFIRMADO (testar):
  - Sugerir validar em VM: instalar, executar `AkVCamManager.exe dump` e checar se o dispositivo aparece sem reboot.
- Assinatura do driver e compatibilidade com Secure Boot / WDAC / Driver Signature Enforcement — NÃO CONFIRMADO (testar/avaliar):
  - Para ambientes com Secure Boot/WDAC estritos, drivers sem assinatura podem ser bloqueados. Sugerir verificar arquivos do driver (.sys/.cat) com `signtool verify /kp` e testar instalação em máquina com Secure Boot ativado.
- Dependências de runtime dos binários `ffmpeg`/`ffprobe` em Windows — NÃO CONFIRMADO (mitigar):
  - Recomenda-se usar builds estáticos de FFmpeg para Windows (MinGW/Static) para evitar dependências de VC++ redistributable; documentar qual build foi empacotado e, se necessário, incluir VC++ redistributable no bundle ou instruções para instalá-lo offline.
- Versão correta do `ffprobe.exe` a embutir (compatibilidade com heurísticas de probing em [`camera_virtual_fratar/video_probe.py`](camera_virtual_fratar/video_probe.py:1)) — NÃO CONFIRMADO (testar):
  - Validar que a versão incluída responde aos parâmetros usados pelo código (ex.: `-v error -select_streams v:0 -show_entries stream=...`).

## É possível ser 100% autossuficiente?
- Sim — em Windows 10/11 é viável criar um fluxo 100% autossuficiente usando PyInstaller onefile:
  - O runtime Python fica empacotado no exe.
  - Binários nativos (ffmpeg, ffprobe, AkVCamManager.exe, instalador akvirtualcamera) podem ser incluídos via `--add-binary` (como já feito em [`build.bat`](build.bat:49)).
  - A única exigência no cliente é executar com privilégios administrativos para instalar o driver.
- Pontos de atenção:
  - Flags silenciosas do instalador do driver.
  - Assinatura de código/driver para evitar bloqueios (SmartScreen/Driver Signature Enforcement).
  - Políticas corporativas que podem impedir instalação de drivers não assinados.

## Referências oficiais (confirmadas)
- AKVirtualCamera — Wiki "Build and install" (documenta o uso de `AkVCamManager.exe --install` e `sc start AkVCamManager`):
  - https://github.com/webcamoid/akvirtualcamera/wiki/Build-and-install
- AKVirtualCamera — Usage and examples:
  - https://github.com/webcamoid/akvirtualcamera/wiki/Usage-and-examples
- AKVirtualCamera — Configure the cameras:
  - https://github.com/webcamoid/akvirtualcamera/wiki/Configure-the-cameras
- AKVirtualCamera — Wiki (índice geral):
  - https://github.com/webcamoid/akvirtualcamera/wiki
- FFmpeg — licensing e builds para Windows (informações sobre builds estáticos e licenciamento):
  - https://ffmpeg.org/legal.html
  - https://www.gyan.dev/ffmpeg/builds/ (exemplo de builds estáticos para Windows)
- Microsoft Docs — Known Folders (Startup), Task Scheduler, e políticas de assinatura de código/driver:
  - Known Folders / Startup: https://learn.microsoft.com/windows/win32/shell/knownfolderid
  - Task Scheduler: https://learn.microsoft.com/windows/win32/taskschd/task-scheduler-start-page
  - Kernel-mode Code Signing Policy / Driver Signing: https://learn.microsoft.com/windows-hardware/drivers/install/kernel-mode-code-signing
  - WDAC / Device Guard: https://learn.microsoft.com/windows/security/threat-protection/windows-defender-application-control/get-started-with-wdac
  - SmartScreen overview: https://learn.microsoft.com/windows/security/identity-protection/windows-defender-smartscreen/windows-defender-smartscreen-overview

## Fluxo atualizado (Mermaid) — onefile offline
```mermaid
graph TD
  A[Onefile exe (virtual_camera_fratar.exe)] --> B[Solicita UAC (elevação)]
  B --> C[Executa instalação do driver (AkVCamManager.exe --install | instalador silencioso fallback)]
  C --> D[Cria device (add-device, add-format, update) via [`create_virtual_camera()`](camera_virtual_fratar/virtual_camera.py:152)]
  D --> E[Valida via `AkVCamManager.exe dump` / logs]
  E --> F[Copia exe para Startup -> inicia app]
  F --> G[Device disponível para apps (Zoom/OBS) e streaming via ffmpeg/ffprobe]
```

## Checklist final de recomendações para o time de build
- Garantir que [`build.bat`](build.bat:1) inclua todo o seguinte:
  - `--add-binary` para `ffmpeg.exe`
  - `--add-binary` para `AkVCamManager.exe`
  - `--add-binary` para `AkVCamManager.exe` (novo; suporte ao fluxo preferido de instalação com o Assistant)
  - `--add-binary` para `akvirtualcamera-windows-9.1.2.exe`
  - Adicionar `--add-binary "ffprobe.exe;."` (sugestão) para garantir probing robusto (documentar como linha sugerida em [`build.bat`](build.bat:51)).
- Validar e documentar flags silenciosas do instalador do AKVirtualCamera.
- Implementar o comando `--install` conforme proposta operacional (elevação, instalação do driver, criação do device, deploy para Startup, logs).
- Assinar binários/driver e incluir instrução de desbloqueio para testes internos.

---
Documento atualizado para contemplar fluxo 100% offline/autossuficiente (onefile exe). As seções adicionadas/atualizadas: "Instalação 100% offline e autossuficiente", "Como o comando --install deve operar (proposta operacional)", "Passo a passo para instalação no cliente" (atualizado para modo offline), "Recomendações de build para 100% offline", "Verificações pós-instalação (offline)" (detalhado), "Troubleshooting (offline)", "Gaps de informação", "É possível ser 100% autossuficiente?" e ajuste do fluxograma Mermaid para o cenário offline.