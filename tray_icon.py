import tkinter as tk
import pystray
from PIL import Image, ImageDraw
import threading
import logging

logging.info("Importações do tray_icon.py concluídas")

class SimpleWindow:
    def __init__(self, hide_callback):
        logging.info("Iniciando SimpleWindow")
        self.root = tk.Tk()
        self.root.title("Teste")
        self.root.geometry("300x200")
        self.hide_callback = hide_callback

        label = tk.Label(self.root, text="FUNCIONOU", font=("Arial", 24))
        label.pack(expand=True)

        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.withdraw()  # Inicialmente escondida
        logging.info("SimpleWindow inicializada e escondida")

    def on_closing(self):
        logging.info("Fechando SimpleWindow")
        self.hide()
        if self.hide_callback:
            self.hide_callback()

    def show(self):
        logging.info("Mostrando SimpleWindow")
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
        self.root.update()

    def hide(self):
        logging.info("Escondendo SimpleWindow")
        self.root.withdraw()

    def is_visible(self):
        visible = self.root.state() == 'normal'
        logging.info(f"SimpleWindow visível: {visible}")
        return visible

    def run(self):
        logging.info("Iniciando loop principal da SimpleWindow")
        if not self.root.winfo_exists():
            logging.warning("A janela Tkinter não existe mais. Criando uma nova.")
            self.__init__(self.hide_callback)
        self.root.mainloop()

    def destroy(self):
        logging.info("Destruindo SimpleWindow")
        self.root.quit()
        self.root.destroy()

class VirtualCameraApp:
    def __init__(self):
        logging.info("Iniciando VirtualCameraApp")
        self.icon = None
        self.window = None
        self.create_tray_icon()
        self.tray_thread = None

    def create_image(self):
        logging.info("Criando imagem para o ícone de bandeja")
        width = 64
        height = 64
        color1 = (255, 0, 0)  # Vermelho
        color2 = (255, 255, 255)  # Branco
        image = Image.new('RGB', (width, height), color1)
        dc = ImageDraw.Draw(image)
        dc.rectangle([width // 2, 0, width, height], fill=color2)
        return image

    def create_tray_icon(self):
        logging.info("Criando ícone de bandeja")
        image = self.create_image()
        menu = pystray.Menu(
            pystray.MenuItem("Abrir", self.toggle_window),
            pystray.MenuItem("Sair", self.quit_app)
        )
        self.icon = pystray.Icon("VirtualCamera", image, "Câmera Virtual Fratar", menu)
        logging.info("Ícone de bandeja criado")

    def run_tray_icon(self):
        logging.info("Iniciando execução do ícone de bandeja")
        self.window = SimpleWindow(hide_callback=self.update_menu_state)
        self.tray_thread = threading.Thread(target=self.icon.run, daemon=True)
        self.tray_thread.start()
        logging.info("Thread do ícone de bandeja iniciada")
        self.window.run()
        logging.info("Janela principal em execução")

    def toggle_window(self, icon, item):
        logging.info("Alternando visibilidade da janela")
        if self.window.is_visible():
            self.window.hide()
        else:
            self.window.show()
        self.update_menu_state()

    def update_menu_state(self):
        logging.info("Atualizando estado do menu")
        if self.icon:
            new_text = "Fechar" if self.window.is_visible() else "Abrir"
            self.icon.menu = pystray.Menu(
                pystray.MenuItem(new_text, self.toggle_window),
                pystray.MenuItem("Sair", self.quit_app)
            )
        logging.info(f"Menu atualizado: {new_text}")

    def quit_app(self, icon, item):
        logging.info("Encerrando aplicação")
        if self.window:
            self.window.destroy()
        icon.stop()
        logging.info("Aplicação encerrada")
