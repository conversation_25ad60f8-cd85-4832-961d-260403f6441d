import os
import sys
import logging
import tempfile

def get_log_path():
    """Determina o caminho apropriado para o arquivo de log"""
    try:
        # Tenta usar o diretório temporário do sistema
        temp_dir = tempfile.gettempdir()
        return os.path.join(temp_dir, 'camera_virtual_fratar.log')
    except Exception:
        # Fallback para diretório atual
        return 'camera_virtual_fratar.log'

def setup_logging():
    try:
        # Determina caminho do arquivo de log
        log_file = get_log_path()
        
        # Remove handlers existentes para evitar duplicação
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        
        # Configura formato básico
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        
        # Handler para arquivo (manter DEBUG completo no arquivo)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logging.root.addHandler(file_handler)
        
        # Configura logger root no nível mais baixo; handlers controlam o que aparece onde
        logging.root.setLevel(logging.DEBUG)

        # Handler para console (quando disponível) — voltado para UX CLI (mostra INFO+ no stdout)
        try:
            stream = getattr(sys, "stdout", None)
            if stream is not None:
                console_handler = logging.StreamHandler(stream=stream)
                console_handler.setLevel(logging.INFO)  # não poluir console com DEBUG
                console_handler.setFormatter(formatter)
                logging.root.addHandler(console_handler)
        except Exception:
            # Falha ao adicionar console handler não deve impedir logging em arquivo
            pass
        
        # Log inicial
        logging.info(f"Logging iniciado. Arquivo de log: {log_file}")
        
    except Exception as e:
        # Se falhar, usa configuração mínima
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        print(f"Erro ao configurar logging: {str(e)}")