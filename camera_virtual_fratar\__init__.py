"""Virtual Camera Fratar core package."""

from .logging_setup import setup_logging
from .single_instance import ensure_single_instance
from .streaming import (
    StreamingProcess,
    check_connected_clients,
    start_streaming,
    stop_streaming,
)
from .system_checks import verify_app_requirements
from .virtual_camera import (
    create_virtual_camera,
    reset_virtual_camera,
    virtual_camera_exists,
)

__all__ = [
    "StreamingProcess",
    "check_connected_clients",
    "create_virtual_camera",
    "ensure_single_instance",
    "reset_virtual_camera",
    "setup_logging",
    "start_streaming",
    "stop_streaming",
    "verify_app_requirements",
    "virtual_camera_exists",
]
