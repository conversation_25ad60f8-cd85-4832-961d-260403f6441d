import os
import sys
import ctypes
import subprocess
import logging
from .virtual_camera import resource_path, verify_camera_driver

def verify_ffmpeg():
    try:
        ffmpeg_path = resource_path("ffmpeg.exe")
        if not os.path.exists(ffmpeg_path):
            logging.error("ffmpeg.exe não encontrado no diretório do programa")
            return False
            
        result = subprocess.run([ffmpeg_path, "-version"], 
                              capture_output=True,
                              text=True)
        if result.returncode != 0:
            logging.error(f"Erro ao executar FFmpeg: {result.stderr}")
            return False
            
        return True
    except Exception as e:
        logging.error(f"Erro ao verificar FFmpeg: {e}")
        return False

def check_system_resources():
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            logging.warning("Aplicativo rodando sem privilégios de administrador")
            
        app_dir = os.path.dirname(os.path.abspath(sys.executable))
        test_file = os.path.join(app_dir, "test_write.tmp")
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except:
            logging.error("Sem permissão de escrita no diretório do programa")
            return False
            
        return True
    except Exception as e:
        logging.error(f"Erro ao verificar recursos do sistema: {e}")
        return False

def verify_app_requirements():
    """
    Verifica requisitos e fornece saída ampliada em caso de falha.
    Escreve no logger padrão e também tenta gravar um arquivo de debug
    no diretório atual para casos em que o logger não esteja acessível.
    """
    checklist = {
        "Driver da Câmera": verify_camera_driver(),
        "FFmpeg": verify_ffmpeg(),
        "Recursos do Sistema": check_system_resources(),
    }

    # Log individual de cada checagem para facilitar diagnóstico
    for component, status in checklist.items():
        if status:
            logging.info("Verificação OK: %s", component)
        else:
            logging.error("Verificação FALHOU: %s", component)

    all_ok = all(checklist.values())

    if not all_ok:
        error_msg = "Problemas encontrados:\n"
        for component, status in checklist.items():
            if not status:
                error_msg += f"- {component}: Falha na verificação\n"

        # Log principal
        logging.error(error_msg)

        # Tentativa de fallback: gravar arquivo de debug no diretório atual
        try:
            debug_path = os.path.join(os.getcwd(), "camera_virtual_fratar_debug.log")
            with open(debug_path, "a", encoding="utf-8") as f:
                f.write(error_msg + "\n")
        except Exception:
            # não bloquear a execução se a escrita falhar
            pass

        # Também imprimir no stdout para que quem executa veja imediatamente
        try:
            print(error_msg, flush=True)
        except Exception:
            pass
    else:
        logging.info("verify_app_requirements: todos os requisitos atendidos")

    return all_ok