from __future__ import annotations

import argparse
import logging
import sys
import threading
import time
import traceback
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Optional, Any

import ctypes
import subprocess
import shutil
import os
from pathlib import Path

import pystray
from PIL import Image, ImageDraw

from camera_virtual_fratar.logging_setup import setup_logging
from camera_virtual_fratar.single_instance import ensure_single_instance
from camera_virtual_fratar import streaming as streaming_mod
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from camera_virtual_fratar.streaming import StreamingProcess
from camera_virtual_fratar.system_checks import verify_app_requirements
from camera_virtual_fratar.virtual_camera import (
    create_virtual_camera,
    reset_virtual_camera,
    virtual_camera_exists,
    resource_path,
    verify_camera_driver,
    get_dump_info,
    run_akvcam_command,
    ensure_device_for_format,
    remove_virtual_camera,
)


# Diretório oficial de instalação dos binários AkVirtualCamera (x64)
AKVCAM_BIN_DIR = r"C:\Program Files\AkVirtualCamera\x64"


def ensure_akvcam_bin_on_path() -> None:
    """
    Garante que 'C:\Program Files\AkVirtualCamera\x64' esteja no PATH do processo,
    permitindo resolver AkVCamManager.exe/AkVCamAssistant.exe via PATH.
    """
    try:
        if os.path.isdir(AKVCAM_BIN_DIR):
            current = os.environ.get("PATH", "")
            parts = current.split(os.pathsep) if current else []
            if AKVCAM_BIN_DIR not in parts:
                os.environ["PATH"] = AKVCAM_BIN_DIR + (os.pathsep + current if current else "")
                logging.info("AKVCAM_BIN_DIR adicionado ao PATH: %s", AKVCAM_BIN_DIR)
        else:
            logging.debug("AKVCAM_BIN_DIR não encontrado: %s", AKVCAM_BIN_DIR)
    except Exception as exc:
        logging.exception("Erro ao ajustar PATH para AKVCAM_BIN_DIR: %s", exc)


def _find_in_akvcam_dir(candidates: list[str]) -> str | None:
    """
    Retorna o primeiro executável existente dentro de AKVCAM_BIN_DIR a partir da lista de 'candidates'.
    """
    try:
        for name in candidates:
            p = os.path.join(AKVCAM_BIN_DIR, name)
            if os.path.isfile(p):
                return p
        return None
    except Exception:
        return None


def get_manager_exe() -> str:
    """
    Resolve o caminho do AkVCamManager.exe:
      - Tenta variações de capitalização dentro de AKVCAM_BIN_DIR.
      - Se não encontrar, retorna 'AkVCamManager.exe' para resolução via PATH.
    """
    return _find_in_akvcam_dir(
        ["AkVCamManager.exe", "akvcammanager.exe", "AkCamManager.exe", "akcammanager.exe"]
    ) or "AkVCamManager.exe"


def get_assistant_exe() -> str:
    """
    Resolve o caminho do AkVCamAssistant.exe:
      - Tenta variações de capitalização dentro de AKVCAM_BIN_DIR.
      - Se não encontrar, retorna 'AkVCamAssistant.exe' para resolução via PATH.
    """
    return _find_in_akvcam_dir(["AkVCamAssistant.exe", "akvcamassistant.exe"]) or "AkVCamAssistant.exe"


def run_manager_command(args: list[str]) -> subprocess.CompletedProcess:
    """
    Executa AkVCamManager com captura de stdout/stderr.
    """
    try:
        ensure_akvcam_bin_on_path()
    except Exception:
        pass
    cmd = [get_manager_exe(), *args]
    logging.debug("Executando AkVCamManager: %s", cmd)
    return subprocess.run(cmd, capture_output=True, text=True)


def run_assistant_command(args: list[str]) -> subprocess.CompletedProcess:
    """
    Executa AkVCamAssistant com captura de stdout/stderr.
    """
    try:
        ensure_akvcam_bin_on_path()
    except Exception:
        pass
    cmd = [get_assistant_exe(), *args]
    logging.debug("Executando AkVCamAssistant: %s", cmd)
    return subprocess.run(cmd, capture_output=True, text=True)


def log_uncaught_exceptions(exc_type, exc_value, exc_traceback):
    logging.error("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))


sys.excepthook = log_uncaught_exceptions


class VirtualCameraApp:
    def __init__(self) -> None:
        self.root = tk.Tk()
        self.root.title("Câmera Virtual Fratar")
        self.root.geometry("320x180")
        self.root.protocol("WM_DELETE_WINDOW", self.hide_window)

        self.status_label = ttk.Label(self.root, text="", justify="left")
        self.status_label.pack(pady=20)

        # Estados exibidos ao usuário na janela principal.
        self.monitor_status = "Parado"
        self.ffmpeg_status = "Parado"
        self.connected_clients = 0
        # Atualiza imediatamente o texto inicial antes do loop do Tk.
        self._set_label_text(self._compose_status_text(), force_now=True)

        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=10)

        start_button = ttk.Button(button_frame, text="Iniciar", command=self.start_camera)
        start_button.grid(row=0, column=0, padx=5)

        stop_button = ttk.Button(button_frame, text="Parar", command=self.stop_camera)
        stop_button.grid(row=0, column=1, padx=5)

        self.root.withdraw()

        self.icon: Any = self._create_tray_icon()
        self.tray_thread: Optional[threading.Thread] = None

        self.is_running = False
        self.streaming_process: Optional[StreamingProcess] = None
        self.camera_thread: Optional[threading.Thread] = None

    def _create_tray_icon(self) -> Any:
        image = self._create_image()
        menu = pystray.Menu(
            pystray.MenuItem("Abrir", self._on_tray_toggle),
            pystray.MenuItem("Sair", self._on_tray_quit),
        )
        return pystray.Icon("VirtualCamera", image, "Câmera Virtual Fratar", menu)

    def _create_image(self) -> Image.Image:
        width, height = 64, 64
        color1 = (255, 0, 0)
        color2 = (255, 255, 255)
        image = Image.new("RGB", (width, height), color1)
        dc = ImageDraw.Draw(image)
        dc.rectangle([width // 2, 0, width, height], fill=color2)
        return image

    def _on_tray_toggle(self, icon, item) -> None:
        self.root.after(0, self.toggle_window)

    def _on_tray_quit(self, icon, item) -> None:
        self.root.after(0, self.quit_app)

    def toggle_window(self) -> None:
        if self.root.state() == "withdrawn":
            self.show_window()
        else:
            self.hide_window()

    def show_window(self) -> None:
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()

    def hide_window(self) -> None:
        self.root.withdraw()

    def _start_tray_icon(self) -> None:
        if self.tray_thread and self.tray_thread.is_alive():
            return
        try:
            run_method = getattr(self.icon, "run", None)
            if callable(run_method):
                self.tray_thread = threading.Thread(target=run_method, daemon=True)
                self.tray_thread.start()
            else:
                logging.error("pystray.Icon.run não disponível; ícone de bandeja não será iniciado")
        except Exception:
            logging.exception("Erro ao iniciar ícone de bandeja")

    def _stop_tray_icon(self) -> None:
        if self.icon:
            try:
                stop_method = getattr(self.icon, "stop", None)
                if callable(stop_method):
                    stop_method()
                else:
                    logging.debug("pystray.Icon.stop não disponível; ignorando")
            except Exception:
                logging.exception("Erro ao parar ícone de bandeja")
        if self.tray_thread and self.tray_thread.is_alive():
            try:
                self.tray_thread.join(timeout=2)
            except Exception:
                logging.debug("Falha ao aguardar thread do ícone encerrar", exc_info=True)

    def start_camera(self) -> None:
        if self.is_running:
            return
        self.is_running = True
        self.update_status("Monitorando")
        self.update_ffmpeg_status("Parado")
        self.update_client_count(0)
        if self.camera_thread is None or not self.camera_thread.is_alive():
            self.camera_thread = threading.Thread(target=self._camera_loop, daemon=True)
            self.camera_thread.start()

    def stop_camera(self) -> None:
        if not self.is_running:
            return
        self.is_running = False
        if self.streaming_process is not None:
            self.streaming_process = streaming_mod.stop_streaming(self.streaming_process)
        self.update_status("Parado")
        self.update_ffmpeg_status("Parado")
        self.update_client_count(0)

    def _camera_loop(self) -> None:
        logging.info("Iniciando loop da câmera virtual")
        try:
            if not virtual_camera_exists():
                if not create_virtual_camera():
                    logging.error("Não foi possível criar a câmera virtual")
                    self.update_status("Falha na câmera")
                    self.update_ffmpeg_status("Parado")
                    self.update_client_count(0)
                    return

            while self.is_running:
                try:
                    clients = streaming_mod.check_connected_clients()
                    self.update_client_count(clients)

                    # Telemetria de debug: número de clients e estado do streaming_process
                    try:
                        sp = self.streaming_process
                        if sp is None:
                            sp_state = "None"
                        else:
                            ff_pid = getattr(sp.ffmpeg, "pid", None) if getattr(sp, "ffmpeg", None) else None
                            ff_poll = sp.ffmpeg.poll() if getattr(sp, "ffmpeg", None) else None
                            ak_pid = getattr(sp.akvcam, "pid", None) if getattr(sp, "akvcam", None) else None
                            ak_poll = sp.akvcam.poll() if getattr(sp, "akvcam", None) else None
                            sp_state = f"ffmpeg(pid={ff_pid},poll={ff_poll}),akvcam(pid={ak_pid},poll={ak_poll})"
                    except Exception:
                        sp_state = "error_retrieving_state"

                    logging.debug("Loop telemetry: clients=%s, streaming_process=%s", clients, sp_state)

                    # Se o pipeline morreu inesperadamente, garante limpeza e estado consistente.
                    if self.streaming_process is not None and not self._streaming_pipeline_alive():
                        logging.warning("Pipeline de streaming finalizou inesperadamente; limpando estado interno.")
                        self.streaming_process = streaming_mod.stop_streaming(self.streaming_process)
                        self.update_ffmpeg_status("Parado")
                        self.update_status("Monitorando")

                    if clients > 0 and self.streaming_process is None:
                        logging.info("Iniciando streaming para %s cliente(s)", clients)
                        process = streaming_mod.start_streaming()
                        if process is None:
                            logging.error("Falha ao iniciar streaming")
                            self.update_ffmpeg_status("Parado")
                            self.update_status("Monitorando")
                            time.sleep(5)
                            continue
                        self.streaming_process = process
                        self.update_ffmpeg_status("Rodando")
                        self.update_status("Transmitindo")
                    elif clients == 0 and self.streaming_process is not None:
                        logging.info("Nenhum cliente conectado, parando streaming")
                        self.streaming_process = streaming_mod.stop_streaming(self.streaming_process)
                        self.update_ffmpeg_status("Parado")
                        self.update_status("Monitorando")
                    else:
                        if self.streaming_process is None:
                            self.update_ffmpeg_status("Parado")
                            self.update_status("Monitorando")
                        else:
                            self.update_ffmpeg_status("Rodando")
                            self.update_status("Transmitindo")

                    time.sleep(5)
                except Exception as exc:
                    logging.error("Erro no loop da câmera: %s", exc)
                    logging.error(traceback.format_exc())
                    time.sleep(5)

            if self.streaming_process is not None:
                self.streaming_process = streaming_mod.stop_streaming(self.streaming_process)
                self.update_ffmpeg_status("Parado")
                self.update_status("Parado")
                self.update_client_count(0)
        finally:
            logging.info("Loop da câmera virtual finalizado")
            self.update_ffmpeg_status("Parado")
            self.update_status("Parado")
            self.update_client_count(0)

    def _compose_status_text(self) -> str:
        return (
            "Status da Câmera Virtual\n"
            f"Status: {self.monitor_status}\n"
            f"FFMPEG: {self.ffmpeg_status}\n"
            f"Clientes Conectados: {self.connected_clients}"
        )

    def _set_label_text(self, text: str, force_now: bool = False) -> None:
        def _safe_set() -> None:
            try:
                lbl = self.status_label
                if lbl is None:
                    logging.debug("status_label não existe (None)")
                    return

                cfg = getattr(lbl, "config", None)
                if callable(cfg):
                    cfg(text=text)
                    return

                set_text = getattr(lbl, "setText", None)
                if callable(set_text):
                    set_text(text)
                    return

                if hasattr(lbl, "text"):
                    try:
                        setattr(lbl, "text", text)
                    except Exception:
                        logging.debug("Não foi possível definir atributo text no status_label")
                else:
                    logging.debug("status_label não tem método conhecido para atualizar texto")
            except Exception:
                logging.exception("Erro ao atualizar status_label")

        try:
            if force_now:
                _safe_set()
            elif hasattr(self, "root") and getattr(self, "root") is not None and hasattr(self.root, "after"):
                self.root.after(0, _safe_set)
            else:
                _safe_set()
        except Exception:
            logging.exception("Erro ao agendar atualização de status")

    def _refresh_status_label(self) -> None:
        self._set_label_text(self._compose_status_text())

    def _streaming_pipeline_alive(self) -> bool:
        process = self.streaming_process
        if process is None:
            return False
        for proc_name in ("ffmpeg", "akvcam"):
            proc = getattr(process, proc_name, None)
            if proc is None:
                return False
            try:
                if proc.poll() is not None:
                    return False
            except Exception:
                return False
        return True

    def update_status(self, status: str) -> None:
        """
        Atualiza o estado geral exibido na interface (linha "Status").
        Evita trabalho desnecessário quando não há mudança.
        """
        if getattr(self, "monitor_status", None) == status:
            return
        self.monitor_status = status
        self._refresh_status_label()
        logging.info("Status da câmera: %s", status)

    def update_ffmpeg_status(self, status: str) -> None:
        if getattr(self, "ffmpeg_status", None) == status:
            return
        self.ffmpeg_status = status
        self._refresh_status_label()
        logging.info("Status do FFMPEG: %s", status)

    def update_client_count(self, count: int) -> None:
        try:
            normalized = max(0, int(count))
        except Exception:
            normalized = 0
        if getattr(self, "connected_clients", None) == normalized:
            return
        self.connected_clients = normalized
        self._refresh_status_label()
        logging.info("Clientes conectados: %s", normalized)

    def quit_app(self) -> None:
        logging.info("Encerrando aplicação")
        self.stop_camera()
        self._stop_tray_icon()
        self.root.quit()

    def run(self) -> None:
        self._start_tray_icon()
        try:
            self.root.mainloop()
        finally:
            self._stop_tray_icon()
            if self.camera_thread and self.camera_thread.is_alive():
                self.camera_thread.join(timeout=5)


def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Virtual Camera Fratar - modo normal e instalador offline (--install)"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Força reinstalação da câmera virtual",
    )
    parser.add_argument(
        "--reset",
        action="store_true",
        help="Remove e reinstala completamente a câmera virtual",
    )
    parser.add_argument(
        "--install",
        action="store_true",
        help=(
            "Executa instalação offline (eleva para Administrador, instala driver AKVirtualCamera via instalador oficial, "
            "valida AkVCamManager, garante device/formato idempotente, "
            "copia exe para Startup). Requer privilégios administrativos; o processo será relançado em UAC quando necessário."
        ),
    )
    return parser.parse_args()


def show_error_message(message: str) -> None:
    root: Optional[tk.Tk] = None
    try:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Erro", message)
    finally:
        if root is not None:
            try:
                root.destroy()
            except Exception:
                pass


# ---------------------------
# Funções de instalação offline
# ---------------------------
def is_admin() -> bool:
    """Retorna True se o processo atual tem privilégios administrativos (UAC)."""
    try:
        return bool(ctypes.windll.shell32.IsUserAnAdmin())
    except Exception as exc:
        logging.debug("is_admin: falha ao detectar admin: %s", exc)
        return False


def relaunch_as_admin(extra_args: list[str]) -> bool:
    """
    Relança o executável atual com privilégios de Administrador (UAC) usando ShellExecuteW.
    extra_args: lista de argumentos que serão passados ao processo relançado.
    Retorna True se a chamada para iniciar o processo foi feita (ShellExecuteW retornou >32).
    """
    try:
        exe = sys.executable
        # quando empacotado com PyInstaller, sys.executable já aponta para o exe empacotado
        params = " ".join(extra_args)
        logging.info("Tentando relançar em modo elevado: %s %s", exe, params)
        ret = ctypes.windll.shell32.ShellExecuteW(None, "runas", exe, params, None, 1)
        # De acordo com MSDN, retorno > 32 indica sucesso de invocação
        success = int(ret) > 32
        logging.debug("ShellExecuteW retorno=%s success=%s", ret, success)
        return success
    except Exception as exc:
        logging.exception("Falha ao relançar como admin: %s", exc)
        return False


def offline_install() -> bool:
    """
    Fluxo offline/administrativo para instalação do driver e preparação do device,
    utilizando exclusivamente o instalador oficial do AKVirtualCamera com o parâmetro "/s",
    resolvendo os utilitários do AkVirtualCamera a partir de 'C:\\Program Files\\AkVirtualCamera\\x64'
    (via PATH do processo), e incorporando o AkVCamAssistant no processo.
    Passos:
      A) Executar apenas 'akvirtualcamera-windows-9.1.2.exe /s' (nenhuma outra flag).
      B) Após instalar, ajustar PATH com ensure_akvcam_bin_on_path() e executar 'AkVCamAssistant.exe --install'.
      C) Validar o driver via 'AkVCamManager.exe --version' (fallback '-h'); sucesso se rc==0.
      D) Garantir device/formato idempotente (CameraFratar1 com YUY2 1280x720 @ 16).
      E) Copiar exe para Startup (comportamento atual).
    """
    logging.info("Iniciando fluxo offline_install (instalador oficial + Assistant + Manager via Program Files)")
    print("Instalação offline iniciada…", flush=True)

    success = True
    driver_validated = False

    try:
        # Redundância: garantir PATH já no início do fluxo
        ensure_akvcam_bin_on_path()

        # A) Executar EXCLUSIVAMENTE o instalador oficial com "/s"
        try:
            inst_name = "akvirtualcamera-windows-9.1.2.exe"
            inst_path = resource_path(inst_name)
            logging.info("Instalador oficial esperado em: %s", inst_path)
            if os.path.exists(inst_path):
                print(f"⇒ Executando instalador oficial {inst_name} com '/s'…", flush=True)
                p = subprocess.run([inst_path, "/s"], capture_output=True, text=True, check=False)
                logging.info(
                    "%s /s rc=%s stdout=%s stderr=%s",
                    inst_name,
                    p.returncode,
                    (p.stdout or "")[:1000],
                    (p.stderr or "")[:1000],
                )
                if p.returncode == 0:
                    print("✓ Driver instalado via instalador oficial (/s).", flush=True)
                else:
                    success = False
                    logging.error("Instalador retornou código diferente de 0.")
                    print("Instalador retornou erro (ver log).", flush=True)
            else:
                logging.warning("Instalador oficial não encontrado no bundle: %s", inst_path)
                print("Instalador oficial não encontrado; prosseguindo com validações.", flush=True)
        except Exception as exc:
            success = False
            logging.exception("Falha ao executar instalador oficial: %s", exc)
            print("Falha ao executar instalador oficial (ver log).", flush=True)

        # Após instalação, reforçar PATH para Program Files
        ensure_akvcam_bin_on_path()

        # B) Executar AkVCamAssistant --install (logar rc/stdout/stderr; não falhar o fluxo por isso)
        try:
            resolved_assistant = get_assistant_exe()
            logging.info("Executando AkVCamAssistant a partir de: %s", resolved_assistant)
            print("⇒ Executando AkVCamAssistant --install…", flush=True)
            cp_assist = run_assistant_command(["--install"])
            rc = getattr(cp_assist, "returncode", None)
            out = (getattr(cp_assist, "stdout", "") or "")[:1000]
            err = (getattr(cp_assist, "stderr", "") or "")[:1000]
            logging.info("AkVCamAssistant --install rc=%s stdout=%s stderr=%s", rc, out, err)
            if rc != 0:
                success = False
                logging.error("AkVCamAssistant --install retornou código diferente de 0.")
                print("AkVCamAssistant --install reportou erro (ver log).", flush=True)
            else:
                print("✓ AkVCamAssistant --install executado.", flush=True)
        except Exception as exc:
            success = False
            logging.exception("Erro ao executar AkVCamAssistant --install: %s", exc)
            print("Falha ao executar AkVCamAssistant --install (ver log).", flush=True)

        # C) Validação do driver via AkVCamManager --version (fallback -h)
        try:
            resolved_manager = get_manager_exe()
            logging.info("Validando AkVCamManager a partir de: %s", resolved_manager)
            print("⇒ Validando driver (AkVCamManager --version)…", flush=True)
            cp = run_manager_command(["--version"])
            if cp.returncode != 0:
                logging.warning("--version falhou (rc=%s). Tentando '-h'…", cp.returncode)
                cp = run_manager_command(["-h"])
            logging.info(
                "AkVCamManager validação rc=%s stdout=%s stderr=%s",
                cp.returncode,
                (cp.stdout or "")[:1000],
                (cp.stderr or "")[:1000],
            )
            if cp.returncode == 0:
                driver_validated = True
                print("✓ Driver validado via AkVCamManager.", flush=True)
            else:
                success = False
                logging.error("Validação do driver falhou (rc != 0).")
                print("Validação do driver falhou.", flush=True)
        except Exception as exc:
            success = False
            logging.exception("Erro durante validação via AkVCamManager: %s", exc)
            print("Falha na validação via AkVCamManager (ver log).", flush=True)

        # D) Garantia idempotente do device/formato (somente se driver respondeu)
        if driver_validated:
            try:
                ensure_akvcam_bin_on_path()  # reforço antes de usar APIs que invocam Manager
                logging.info("Garantindo device/formato via ensure_device_for_format(YUY2 1280x720@16)")
                print("⇒ Garantindo device CameraFratar1 (YUY2 1280x720 @ 16)…", flush=True)
                ok_dev = ensure_device_for_format(1280, 720, 16)
                if not ok_dev:
                    success = False
                    logging.warning("ensure_device_for_format retornou False. Coletando 'devices'…")
                    try:
                        devices_out = run_akvcam_command(["devices"])
                        logging.info("AkVCamManager devices:\n%s", devices_out)
                        if "CameraFratar1" in devices_out:
                            logging.info("Removendo device inconsistente e recriando…")
                            remove_virtual_camera()
                            run_akvcam_command(["update"])
                            ok_dev = ensure_device_for_format(1280, 720, 16)
                    except Exception as exc:
                        logging.exception("Falha ao diagnosticar/recriar device: %s", exc)
                if not ok_dev:
                    logging.error("Falha ao garantir device/formato.")
                    print("Falha ao criar/configurar o device virtual.", flush=True)
                else:
                    print("✓ Device pronto.", flush=True)
            except Exception as exc:
                success = False
                logging.exception("Erro ao garantir device/formato: %s", exc)
                print("Falha em criação do device: veja o log para detalhes.", flush=True)
        else:
            logging.warning("Driver não validado; pulando etapa de criação/garantia de device.")
            success = False

        # Verificação final da existência do device (somente se driver validado)
        if driver_validated:
            try:
                if not virtual_camera_exists():
                    success = False
                    logging.error("Após garantia, CameraFratar1 não está presente.")
                    print("Após criação, CameraFratar1 não foi encontrado.", flush=True)
                else:
                    logging.info("Device CameraFratar1 verificado com sucesso após garantia.")
                    print("Device CameraFratar1 verificado com sucesso.", flush=True)
            except Exception:
                success = False
                logging.exception("Erro ao verificar existência do device após criação.")
                print("Erro ao verificar existência do device após criação.", flush=True)

    except Exception as exc:
        success = False
        logging.exception("Erro inesperado no fluxo offline_install: %s", exc)
    finally:
        # E) Copiar exe para Startup do usuário (per-user) — SEMPRE
        try:
            appdata = os.getenv("APPDATA")
            if not appdata:
                logging.error("APPDATA não definido; não foi possível copiar para Startup.")
                print("APPDATA não definido; não foi possível copiar para Startup.", flush=True)
            else:
                startup_dir = os.path.join(appdata, "Microsoft", "Windows", "Start Menu", "Programs", "Startup")
                os.makedirs(startup_dir, exist_ok=True)
                dst_path = os.path.join(startup_dir, "virtual_camera_fratar.exe")
                try:
                    # Determina a origem correta do executável (PyInstaller onefile)
                    src = None
                    try:
                        if getattr(sys, "frozen", False) and str(sys.argv[0]).lower().endswith(".exe"):
                            src = os.path.abspath(sys.argv[0])
                    except Exception:
                        src = None

                    if not src:
                        logging.info("Execução em modo fonte; cópia para Startup ignorada.")
                        print("Execução em modo fonte; cópia para Startup ignorada.", flush=True)
                    else:
                        logging.info("Copiando %s -> %s", src, dst_path)
                        print("⇒ Copiando executável para Inicializar do Windows…", flush=True)
                        shutil.copy2(src, dst_path)
                        logging.info("Cópia para Startup concluída: %s", dst_path)
                        print(f"✓ Cópia concluída: {dst_path}", flush=True)
                except Exception as cexc:
                    success = False
                    logging.exception("Falha ao copiar exe para Startup: %s", cexc)
                    print("Falha em cópia para Startup: veja o log para detalhes.", flush=True)
        except Exception as exc:
            success = False
            logging.exception("Erro ao preparar cópia para Startup: %s", exc)
            print(f"Erro ao preparar cópia para Startup: {exc}", flush=True)

    if success:
        logging.info("Fluxo offline_install concluído com sucesso.")
        print("Concluído com sucesso.", flush=True)
    else:
        logging.warning("Fluxo offline_install concluído com falhas. Verifique logs.")
        print("Concluído com falhas. Verifique o log para detalhes.", flush=True)
    return success


def main() -> None:
    mutex_handle = ensure_single_instance()

    try:
        try:
            setup_logging()
        except Exception as exc:
            print(f"Erro ao iniciar logging: {exc}")

        # Garantir que sys.stdout / sys.stderr existam (evita AttributeError em ambientes sem console)
        # (ex.: executáveis empacotados sem console onde argparse tenta escrever a ajuda)
        if getattr(sys, "stdout", None) is None:
            try:
                sys.stdout = sys.__stdout__ or open(os.devnull, "w", encoding="utf-8")
            except Exception:
                sys.stdout = open(os.devnull, "w", encoding="utf-8")
        if getattr(sys, "stderr", None) is None:
            try:
                sys.stderr = sys.__stderr__ or open(os.devnull, "w", encoding="utf-8")
            except Exception:
                sys.stderr = open(os.devnull, "w", encoding="utf-8")

        # Garante resolução via Program Files antes de usar módulos que dependem de AkVCamManager/Assistant
        ensure_akvcam_bin_on_path()

        args = parse_arguments()
        # Feedback CLI imediato quando modo --install é solicitado
        try:
            if getattr(args, "install", False):
                print("Modo de instalação offline requisitado (--install)", flush=True)
        except Exception:
            # não bloquear se stdout não estiver disponível
            pass

        # Modo de instalação offline (--install)
        if getattr(args, "install", False):
            logging.info("Modo de instalação offline requisitado (--install)")
            # Se não estiver com privilégios de admin, relança com UAC
            try:
                print("Verificando privilégios administrativos…", flush=True)
                if not is_admin():
                    logging.info("Processo não está em modo administrador; solicitando UAC e relançando.")
                    print("Solicitando elevação UAC e relançando… este processo encerrará.", flush=True)
                    rel = relaunch_as_admin(["--install"])
                    if rel:
                        logging.info("Pedido de relançamento com UAC enviado com sucesso; encerrando processo atual.")
                        print("UAC solicitado. Encerrando processo atual. Código de saída: 0", flush=True)
                        sys.exit(0)
                    else:
                        logging.error("Falha ao invocar ShellExecuteW para relançamento elevado.")
                        print("Falha ao iniciar processo elevado via UAC. Código de saída: 1", flush=True)
                        sys.exit(1)
                else:
                    print("Privilégios administrativos confirmados.", flush=True)
            except Exception:
                logging.exception("Erro durante checagem/relançamento de elevação UAC.")
                print("Erro durante checagem/relançamento de elevação UAC. Código de saída: 1", flush=True)
                sys.exit(1)

            # Estamos elevados — executa fluxo offline
            try:
                print("Instalação offline iniciada…", flush=True)
                ok = offline_install()
                if not ok:
                    logging.error("offline_install relatou falha; encerrando com erro.")
                    print("offline_install relatou falha; encerrando com erro (código 2)", flush=True)
                    sys.exit(2)
                logging.info("offline_install finalizado com sucesso.")
                print("offline_install finalizado com sucesso. Encerrando com código 0", flush=True)
                sys.exit(0)
            except Exception:
                logging.exception("Exceção inesperada durante offline_install.")
                print("Exceção inesperada durante offline_install. Código de saída: 3", flush=True)
                sys.exit(3)

        if not verify_app_requirements():
            logging.error("Requisitos do sistema não atendidos")
            try:
                print("Requisitos do sistema não atendidos. Verifique o log para detalhes.", flush=True)
            except Exception:
                pass
            show_error_message(
                "Requisitos do sistema não atendidos. Verifique o log para detalhes."
            )
            return

        if args.reset:
            print("Resetando câmera virtual…", flush=True)
            # Assegura que os executáveis sejam resolvidos a partir de Program Files
            ensure_akvcam_bin_on_path()
            # Fluxo de serviço via AkVCamAssistant: desinstala -> instala (ignorar erros)
            try:
                cp_un = run_assistant_command(["--uninstall"])
                logging.info(
                    "AkVCamAssistant --uninstall rc=%s stdout=%s stderr=%s",
                    getattr(cp_un, "returncode", None),
                    (getattr(cp_un, "stdout", "") or "")[:1000],
                    (getattr(cp_un, "stderr", "") or "")[:1000],
                )
            except Exception as exc:
                logging.warning("Falha ao executar AkVCamAssistant --uninstall: %s", exc)
            try:
                cp_in = run_assistant_command(["--install"])
                logging.info(
                    "AkVCamAssistant --install rc=%s stdout=%s stderr=%s",
                    getattr(cp_in, "returncode", None),
                    (getattr(cp_in, "stdout", "") or "")[:1000],
                    (getattr(cp_in, "stderr", "") or "")[:1000],
                )
                if getattr(cp_in, "returncode", 0) != 0:
                    logging.warning("AkVCamAssistant --install retornou código != 0.")
            except Exception as exc:
                logging.warning("Falha ao executar AkVCamAssistant --install: %s", exc)

            if not reset_virtual_camera():
                print("Falha ao resetar a câmera virtual. Verifique o log.", flush=True)
                print("Reset da câmera virtual: ERRO", flush=True)
                show_error_message("Falha ao resetar a câmera virtual.")
                return
            print("Reset da câmera virtual: OK", flush=True)
        else:
            print(f"Preparando câmera virtual{' (force)' if args.force else ''}…", flush=True)
            ensure_akvcam_bin_on_path()
            if not create_virtual_camera(force=args.force):
                print("Preparação da câmera virtual: ERRO", flush=True)
                show_error_message("Falha ao preparar a câmera virtual.")
                return
            print("Preparação da câmera virtual: OK", flush=True)

        app = VirtualCameraApp()
        app.start_camera()
        app.run()
    finally:
        import win32api

        win32api.CloseHandle(mutex_handle)


if __name__ == "__main__":
    main()
