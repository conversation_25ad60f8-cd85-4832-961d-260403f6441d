import logging
import shutil
import subprocess
import re
from typing import Optional, Dict
from .virtual_camera import resource_path

# Utilitário para descobrir resolução/fps/pix_fmt de uma URL de vídeo
# Estratégia:
# 1) Tenta ffprobe com comando direto (mais confi<PERSON>)
# 2) Se ffprobe não estiver disponível ou falhar, faz fallback para ffmpeg -i e parseia stderr
#
# Retorno: dict com keys: width(int), height(int), fps(int), pix_fmt(str|None)
# Em caso de falha retorna None.


_common_pixfmts = {
    "yuv420p",
    "yuyv422",
    "yuv422p",
    "nv12",
    "rgb24",
    "rgba",
    "uyvy422",
    "yuvj420p",
    "yuv444p",
}


def _clamp_fps(f: float) -> int:
    try:
        v = int(round(f))
    except Exception:
        return 30
    if v < 1:
        return 1
    if v > 60:
        return 60
    return v


def _parse_fraction_or_float(s: str) -> Optional[float]:
    s = s.strip()
    if "/" in s:
        try:
            num, den = s.split("/", 1)
            return float(num) / float(den)
        except Exception:
            return None
    try:
        return float(s)
    except Exception:
        return None


def probe_with_ffprobe(url: str) -> Optional[Dict]:
    ffprobe_path = resource_path("ffprobe.exe")
    if not shutil.which(ffprobe_path) and not ffprobe_path:
        # resource_path may return fallback non-existing path; prefer shutil.which first
        logging.debug("ffprobe não encontrado via resource_path (%s)", ffprobe_path)
    cmd = [
        ffprobe_path,
        "-v",
        "error",
        "-select_streams",
        "v:0",
        "-show_entries",
        "stream=width,height,avg_frame_rate,pix_fmt",
        "-of",
        "default=nokey=1:noprint_wrappers=1",
        url,
    ]
    logging.debug("Executando ffprobe: %s", cmd)
    try:
        proc = subprocess.run(cmd, capture_output=True, text=True, timeout=8, check=False)
    except FileNotFoundError:
        logging.debug("ffprobe não encontrado (FileNotFoundError)")
        return None
    except subprocess.TimeoutExpired:
        logging.error("ffprobe timed out probing URL: %s", url)
        return None
    out = (proc.stdout or "").strip()
    err = (proc.stderr or "").strip()
    if proc.returncode != 0:
        logging.debug("ffprobe retornou código %s stderr=%s", proc.returncode, err[:400])
        # ffprobe pode não existir ou não conseguir abrir a URL
        return None
    # ffprobe - with the chosen output, normally returns 4 lines: width, height, avg_frame_rate, pix_fmt
    lines = [l.strip() for l in out.splitlines() if l.strip()]
    if len(lines) < 3:
        logging.debug("ffprobe output inesperado: %s", out[:400])
        return None
    try:
        width = int(lines[0])
        height = int(lines[1])
        avg_frame_rate = lines[2]
        pix_fmt = lines[3] if len(lines) >= 4 else None
    except Exception:
        logging.debug("Erro ao parsear saída do ffprobe: %s", lines)
        return None
    fps_f = _parse_fraction_or_float(avg_frame_rate) or 30.0
    fps = _clamp_fps(fps_f)
    if pix_fmt:
        pix_fmt = pix_fmt.strip()
    logging.info("Probing (ffprobe) result for %s -> %dx%d@%d %s", url, width, height, fps, pix_fmt)
    return {"width": width, "height": height, "fps": fps, "pix_fmt": pix_fmt}


def probe_with_ffmpeg_fallback(url: str) -> Optional[Dict]:
    ffmpeg_path = resource_path("ffmpeg.exe")
    cmd = [ffmpeg_path, "-hide_banner", "-i", url, "-t", "0", "-f", "null", "-"]
    logging.debug("Executando ffmpeg fallback: %s", cmd)
    try:
        proc = subprocess.run(cmd, capture_output=True, text=True, timeout=10, check=False)
    except FileNotFoundError:
        logging.debug("ffmpeg não encontrado (FileNotFoundError)")
        return None
    except subprocess.TimeoutExpired:
        logging.error("ffmpeg fallback timed out probing URL: %s", url)
        return None
    stderr = (proc.stderr or "").splitlines()
    # Procurar linhas que contenham "Stream #... Video:" e parsear resolução e fps/pix_fmt
    video_lines = [l for l in stderr if "Video:" in l]
    if not video_lines:
        logging.debug("Nenhuma linha de vídeo encontrada em ffmpeg stderr: %s", stderr[:10])
        return None
    # Tentar extrair resolução e fps e pix_fmt da primeira linha útil
    line = video_lines[0]
    logging.debug("ffmpeg stream line: %s", line.strip())
    # resolução: procurar \d+x\d+
    res_match = re.search(r"(\d{2,5})x(\d{2,5})", line)
    width = height = None
    if res_match:
        try:
            width = int(res_match.group(1))
            height = int(res_match.group(2))
        except Exception:
            width = height = None
    # fps: procurar padrões como 29.97 fps ou 30000/1001 tbr
    fps = None
    # busca por "<number> fps"
    m = re.search(r"(\d+(?:\.\d+)?)(?:\s*)(?:fps|tbr|TB/s)?", line)
    if m:
        f = _parse_fraction_or_float(m.group(1))
        if f:
            fps = _clamp_fps(f)
    # se não achou, procurar fração em qualquer parte
    if fps is None:
        frac_m = re.search(r"(\d+/\d+)", line)
        if frac_m:
            f = _parse_fraction_or_float(frac_m.group(1))
            if f:
                fps = _clamp_fps(f)
    if fps is None:
        # fallback default
        fps = 30
    # pix_fmt: tentar detectar tokens conhecidos
    pix = None
    # tokens separados por vírgula após "Video:"
    try:
        after_video = line.split("Video:", 1)[1]
        # procurar tokens que casem com _common_pixfmts ou r'\b[a-z0-9]+p\b'
        for token in re.split(r"[,()]+", after_video):
            t = token.strip().lower()
            # detectar padrões como yuv420p, yuyv422, nv12
            if t in _common_pixfmts:
                pix = t
                break
            # heurística: terminado em 'p' e alfanumérico
            if re.match(r"^[a-z0-9]+p$", t):
                pix = t
                break
    except Exception:
        pix = None
    if not (width and height):
        logging.debug("Não foi possível extrair largura/altura do ffmpeg output")
        return None
    logging.info("Probing (ffmpeg fallback) result for %s -> %dx%d@%d %s", url, width, height, fps, pix)
    return {"width": width, "height": height, "fps": fps, "pix_fmt": pix}


def probe_url(url: str) -> Optional[Dict]:
    """
    Proba a URL e retorna dict {width,height,fps,pix_fmt} ou None se falhar.
    """
    try:
        # Primeiro tenta ffprobe (mais direto)
        res = probe_with_ffprobe(url)
        if res:
            return res
        # Fallback para ffmpeg parsing
        res = probe_with_ffmpeg_fallback(url)
        if res:
            return res
        logging.error("Falha no probing da URL: %s (nenhum método obteve resultado)", url)
        return None
    except Exception as e:
        logging.exception("Exceção inesperada durante probing da URL %s: %s", url, e)
        return None