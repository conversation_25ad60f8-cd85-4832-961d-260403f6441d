import logging
import subprocess
import sys
import time
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import List, Optional
import os
import shutil
import shutil


def resource_path(relative_path: str) -> str:
    """
    Resolve a resource path trying multiple locations (helpful in dev and when bundled):

    Order of resolution:
    1. sys._MEIPASS (PyInstaller bundle runtime)
    2. package directory (same folder as this module)
    3. current working directory (repository root when running from source)
    4. PATH lookup via shutil.which for executables (*.exe)
    5. finally returns a best-effort fallback (package dir)

    The function logs which candidate succeeded and returns the absolute path as string.
    """
    candidates = []

    # 1) PyInstaller _MEIPASS if present
    meipass = getattr(sys, "_MEIPASS", None)
    if meipass:
        candidates.append(Path(meipass) / relative_path)

    # 2) package directory (this module's folder)
    candidates.append(Path(__file__).resolve().parent / relative_path)

    # 3) current working directory (repo root when running from project)
    candidates.append(Path.cwd() / relative_path)

    # Evaluate file system candidates and return the first that exists
    for candidate in candidates:
        try:
            resolved = candidate.resolve()
            logging.debug("Checking resource candidate: %s", resolved)
            if resolved.exists():
                logging.info("Resource '%s' resolved to: %s", relative_path, resolved)
                return str(resolved)
        except Exception:
            # ignore resolution errors and try next candidate
            logging.debug("Falha ao resolver candidato de recurso: %s", candidate)

    # 4) PATH lookup for executáveis .exe (ex.: AkVCamManager.exe, ffmpeg.exe)
    try:
        if "\\" not in relative_path and "/" not in relative_path and relative_path.lower().endswith(".exe"):
            which_path = shutil.which(relative_path)
            if which_path:
                logging.info("Resource '%s' encontrado no PATH: %s", relative_path, which_path)
                return which_path
    except Exception:
        logging.debug("Falha ao procurar '%s' no PATH", relative_path)

    # 5) Fallback: return package-dir path even if not existing (keeps existing callers working)
    fallback = (Path(getattr(sys, "_MEIPASS", Path(__file__).resolve().parent)) / relative_path).resolve()
    logging.warning("Resource '%s' não encontrado em candidatos; retornando fallback: %s", relative_path, fallback)
    return str(fallback)


try:
    AKVCAM_TIMEOUT_SECONDS = max(20, int(os.environ.get("AKVCAM_TIMEOUT_SECONDS", "20")))
except ValueError:
    AKVCAM_TIMEOUT_SECONDS = 20

try:
    AKVCAM_ASSIST_TIMEOUT_SECONDS = max(
        AKVCAM_TIMEOUT_SECONDS,
        int(os.environ.get("AKVCAM_ASSIST_TIMEOUT_SECONDS", "60")),
    )
except ValueError:
    AKVCAM_ASSIST_TIMEOUT_SECONDS = max(AKVCAM_TIMEOUT_SECONDS, 60)

AKVCAM_PATH = resource_path("AkVCamManager.exe")


def run_akvcam_command(args: List[str], timeout: int = AKVCAM_TIMEOUT_SECONDS) -> str:
    """Execute AkVCamManager with the provided arguments and return stdout."""
    command = [AKVCAM_PATH, *args]
    logging.debug("Running AkVCamManager command: %s", command)
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=timeout,
            check=False,
        )
    except subprocess.TimeoutExpired as exc:
        logging.error(
            "AkVCamManager command timed out (timeout=%ss): %s", timeout, command
        )
        raise RuntimeError("AkVCamManager command timed out") from exc

    stdout = result.stdout.strip()
    stderr = result.stderr.strip()
    if result.returncode != 0:
        logging.error(
            "AkVCamManager command failed (code %s): %s", result.returncode, stderr or stdout
        )
        raise RuntimeError(stderr or stdout)

    logging.debug("AkVCamManager stdout: %s", stdout)
    return stdout




def _ensure_service_up_via_manager() -> bool:
    """Tenta restaurar o serviço usando o hack seguro do AkVCamManager."""
    try:
        run_akvcam_command(
            ["hack", "set-service-up"],
            timeout=AKVCAM_ASSIST_TIMEOUT_SECONDS,
        )
        logging.info("AkVCamManager hack set-service-up executado com sucesso.")
        return True
    except RuntimeError as exc:
        logging.warning(
            "AkVCamManager hack set-service-up não confirmou sucesso: %s", exc
        )
        return False
    except Exception as exc:
        logging.exception(
            "Erro ao executar AkVCamManager hack set-service-up: %s", exc
        )
        return False


def list_devices() -> str:
    return run_akvcam_command(["devices"])


def list_clients() -> str:
    return run_akvcam_command(["clients"])


def get_dump_info() -> Optional[ET.Element]:
    try:
        xml_string = run_akvcam_command(["dump"])
    except RuntimeError as exc:
        logging.error("Falha ao obter dump do AkVCamManager: %s", exc)
        return None

    if not xml_string:
        logging.error("AkVCamManager retornou dump vazio")
        return None

    try:
        return ET.fromstring(xml_string)
    except ET.ParseError:
        logging.error("Dump XML inválido do AkVCamManager: %s", xml_string[:500])
        return None


def check_clients() -> int:
    root = get_dump_info()
    if root is None:
        return 0

    clients = root.find("clients")
    if clients is None:
        return 0

    return sum(1 for _ in clients)


def virtual_camera_exists() -> bool:
    try:
        devices = list_devices()
    except RuntimeError:
        return False
    return "CameraFratar1" in devices


def create_virtual_camera(force: bool = False) -> bool:
    try:
        if force:
            remove_virtual_camera()

        if virtual_camera_exists() and not force:
            return True

        commands = [
            ["add-device", "-i", "CameraFratar1", "Virtual Camera Fratar 1"],
            ["add-format", "CameraFratar1", "YUY2", "1280", "720", "16"],
            ["update"],
        ]

        for cmd in commands:
            try:
                run_akvcam_command(cmd)
            except RuntimeError as exc:
                logging.error("Erro ao executar comando AkVCamManager %s: %s", cmd, exc)
                return False

        if not virtual_camera_exists():
            logging.error("Falha ao verificar criação da câmera virtual")
            return False
        return True
    except Exception as exc:
        logging.exception("Erro ao criar câmera virtual: %s", exc)
        return False


def verify_camera_driver() -> bool:
    """
    Verifica se AkVCamManager.exe existe e se responde a comandos básicos.
    - Primeira tentativa: 'version'
    - Fallback: 'devices'
    Escreve detalhes no logger e também em camera_virtual_fratar_debug.log.
    """
    try:
        ak_path = Path(AKVCAM_PATH)
        debug_path = Path.cwd() / "camera_virtual_fratar_debug.log"

        def _dbg(line: str) -> None:
            try:
                with debug_path.open("a", encoding="utf-8") as f:
                    f.write(line + "\n")
            except Exception:
                pass

        logging.debug("verify_camera_driver: testando AkVCamManager em %s", ak_path)
        _dbg(f"verify_camera_driver: ak_path={ak_path}")

        # Verifica existência do arquivo
        try:
            exists = ak_path.exists()
        except Exception as e:
            logging.exception("Erro ao testar existência do AkVCamManager: %s", e)
            _dbg(f"exists check error: {e}")
            exists = False

        if not exists:
            msg = f"AkVCamManager.exe não encontrado no caminho testado: {ak_path}"
            logging.error(msg)
            _dbg(msg)
            return False

        def _run(
            cmd_args: List[str], timeout: int = AKVCAM_TIMEOUT_SECONDS
        ) -> subprocess.CompletedProcess:
            _dbg(f"executando: {ak_path} {' '.join(cmd_args)}")
            return subprocess.run(
                [str(ak_path), *cmd_args],
                capture_output=True,
                text=True,
                check=False,
                timeout=timeout,
            )

        # 1) Tenta 'version'
        try:
            # "--version" é a opção oficial documentada; anteriormente usávamos o
            # comando "version" (sem hífen) que não existe e causava o log de
            # "Unknown command 'version'" visto em execuções anteriores.
            result = _run(["--version"])
            stdout = (result.stdout or "").strip()
            stderr = (result.stderr or "").strip()
            logging.debug(
                "AkVCamManager '--version' rc=%s stdout=%s stderr=%s",
                result.returncode, stdout[:1000], stderr[:1000]
            )
            _dbg(f"'--version' rc={result.returncode} stdout={stdout[:200]} stderr={stderr[:200]}")
            if result.returncode == 0:
                logging.info("verify_camera_driver: AkVCamManager respondeu com sucesso (--version).")
                _dbg("--version OK")
                return True
        except subprocess.TimeoutExpired:
            logging.error(
                "AkVCamManager timed out ao executar '--version' (timeout=%ss)",
                AKVCAM_TIMEOUT_SECONDS,
            )
            _dbg("timeout '--version'")
            # continua para fallback
        except Exception as e:
            logging.exception("Falha ao executar AkVCamManager '--version': %s", e)
            _dbg(f"exceção '--version': {e}")
            # continua para fallback

        # 2) Fallback: 'devices'
        try:
            result2 = _run(["devices"])
            out2 = (result2.stdout or "").strip()
            err2 = (result2.stderr or "").strip()
            logging.debug(
                "AkVCamManager 'devices' rc=%s stdout=%s stderr=%s",
                result2.returncode, out2[:1000], err2[:1000]
            )
            _dbg(f"'devices' rc={result2.returncode} stdout={out2[:200]} stderr={err2[:200]}")
            if result2.returncode == 0:
                logging.info("verify_camera_driver: AkVCamManager respondeu com sucesso (devices).")
                _dbg("devices OK")
                return True
        except subprocess.TimeoutExpired:
            logging.error(
                "AkVCamManager timed out ao executar 'devices' (timeout=%ss)",
                AKVCAM_TIMEOUT_SECONDS,
            )
            _dbg("timeout 'devices'")
        except Exception as e:
            logging.exception("Falha ao executar AkVCamManager 'devices': %s", e)
            _dbg(f"exceção 'devices': {e}")

        logging.error("verify_camera_driver: AkVCamManager não respondeu com sucesso a 'version' nem a 'devices'.")
        _dbg("verify_camera_driver: falhou")
        return False
    except Exception as exc:
        logging.exception("Erro inesperado ao verificar driver da câmera: %s", exc)
        try:
            with (Path.cwd() / "camera_virtual_fratar_debug.log").open("a", encoding="utf-8") as f:
                f.write(f"verify_camera_driver: exceção inesperada: {exc}\n")
        except Exception:
            pass
        return False


def remove_virtual_camera() -> bool:
    try:
        devices = list_devices()
        if "CameraFratar1" in devices:
            run_akvcam_command(["remove-device", "CameraFratar1"])
            run_akvcam_command(["update"])
        return True
    except RuntimeError as exc:
        logging.error("Erro ao remover câmera virtual: %s", exc)
        return False
    except Exception as exc:
        logging.exception("Erro inesperado ao remover câmera virtual: %s", exc)
        return False


def reset_virtual_camera() -> bool:
    try:
        remove_virtual_camera()

        reinstall_ok = _ensure_service_up_via_manager()
        if not reinstall_ok:
            logging.warning(
                "Reinstalação do serviço não pôde ser confirmada; prosseguindo com recriação do device."
            )

        time.sleep(2)

        if not verify_camera_driver():
            logging.error("Driver não respondeu após tentativa de reinstalação.")
            return False

        time.sleep(2)

        return create_virtual_camera(force=True)
    except RuntimeError as exc:
        logging.error("Erro ao resetar câmera virtual: %s", exc)
        return False
    except Exception as exc:
        logging.exception("Erro inesperado ao resetar câmera virtual: %s", exc)
        return False

# --- Persistência simples do último formato aplicado (memória + arquivo) ---
import json
from pathlib import Path as _Path

_LAST_FORMAT_PATH = _Path.cwd() / "last_device_format.json"
_LAST_FORMAT_CACHE = None  # tipo: Optional[dict]


def _load_last_format() -> Optional[dict]:
    global _LAST_FORMAT_CACHE
    if _LAST_FORMAT_CACHE is not None:
        return _LAST_FORMAT_CACHE
    try:
        if _LAST_FORMAT_PATH.exists():
            with _LAST_FORMAT_PATH.open("r", encoding="utf-8") as f:
                data = json.load(f)
                _LAST_FORMAT_CACHE = data
                logging.debug("Último formato carregado de %s: %s", _LAST_FORMAT_PATH, data)
                return data
    except Exception as exc:
        logging.debug("Falha ao carregar último formato: %s", exc)
    return None


def _save_last_format(fmt: dict) -> None:
    global _LAST_FORMAT_CACHE
    try:
        with _LAST_FORMAT_PATH.open("w", encoding="utf-8") as f:
            json.dump(fmt, f)
        _LAST_FORMAT_CACHE = fmt
        logging.debug("Último formato salvo em %s: %s", _LAST_FORMAT_PATH, fmt)
    except Exception as exc:
        logging.warning("Falha ao salvar último formato em %s: %s", _LAST_FORMAT_PATH, exc)


def ensure_device_for_format(width: int, height: int, fps: int) -> bool:
    """
    Garante de forma idempotente, com base no ESTADO REAL (não no cache), que:
      - O device 'CameraFratar1' exista.
      - O formato 'YUY2 {width} {height} {fps}' esteja presente.
    Estratégia:
      1) Consultar 'devices'. Se o device não existir, criar: add-device -> add-format -> update.
      2) Se existir, consultar 'formats CameraFratar1'. Se faltar o formato desejado, adicionar e 'update'.
      3) Em caso de erro 'Failed to create device.', consultar 'devices'. Se o ID existir em estado estranho,
         chamar remove_virtual_camera() (sem remover global) e repetir a criação do device.
      4) Após SUCESSO, atualizar last_device_format.json.
    Logs:
      - INFO: passos de alto nível (criando device, adicionando formato, update, formato já presente).
      - DEBUG: stdout dos comandos AkVCamManager.
    """
    desired = {
        "pix_fmt": "YUY2",
        "width": int(width),
        "height": int(height),
        "fps": int(fps),
    }
    try:
        logging.info(
            "Verificando estado real (devices/formats) para garantir YUY2 %dx%d@%d",
            desired["width"], desired["height"], desired["fps"]
        )

        def _format_exists() -> bool:
            try:
                out = run_akvcam_command(["formats", "CameraFratar1"])
                logging.debug("Saída 'formats CameraFratar1': %s", out)
                lowered = out.lower()
                # Verifica presença explícita de YUY2, width, height e fps (tolerando 16, 16/1, 16.0, etc.)
                has_pix = "yuy2" in lowered
                has_wh = str(desired["width"]) in out and str(desired["height"]) in out
                # Tolerância de FPS: inteiro, fração /1 ou ponto flutuante .0+
                import re
                fps = int(desired["fps"])
                fps_ok = bool(
                    re.search(rf"\b{fps}(?:\.0+)?\b", out) or re.search(rf"\b{fps}/1\b", out)
                )
                return has_pix and has_wh and fps_ok
            except RuntimeError as exc:
                logging.debug("Comando 'formats' falhou: %s", exc)
                return False

        def _create_device_sequence() -> bool:
            # add-device
            logging.info("criando device CameraFratar1…")
            try:
                out1 = run_akvcam_command(
                    ["add-device", "-i", "CameraFratar1", "Virtual Camera Fratar 1"]
                )
                logging.debug("stdout add-device: %s", out1)
            except RuntimeError as exc:
                msg = str(exc)
                logging.warning("Falha em 'add-device': %s", msg)
                # Tratar caso 'Failed to create device' ou similar
                if "failed to create device" in msg.lower() or "create device" in msg.lower():
                    try:
                        devs = run_akvcam_command(["devices"])
                        logging.debug("devices após falha de criação: %s", devs)
                        if "CameraFratar1" in devs:
                            logging.info("ID presente em estado estranho; removendo e tentando novamente.")
                            remove_virtual_camera()
                            run_akvcam_command(["update"])
                            out1b = run_akvcam_command(
                                ["add-device", "-i", "CameraFratar1", "Virtual Camera Fratar 1"]
                            )
                            logging.debug("stdout add-device (retry): %s", out1b)
                        else:
                            return False
                    except Exception as inner:
                        logging.exception("Erro ao tratar falha de criação do device: %s", inner)
                        return False
                else:
                    return False

            # add-format
            logging.info(
                "adicionando formato YUY2 %dx%d@%d…",
                desired["width"], desired["height"], desired["fps"]
            )
            try:
                out2 = run_akvcam_command(
                    [
                        "add-format",
                        "CameraFratar1",
                        "YUY2",
                        str(desired["width"]),
                        str(desired["height"]),
                        str(desired["fps"]),
                    ]
                )
                logging.debug("stdout add-format: %s", out2)
            except RuntimeError as exc:
                logging.error("Falha ao adicionar formato: %s", exc)
                return False

            # update
            logging.info("aplicando update…")
            try:
                out3 = run_akvcam_command(["update"])
                logging.debug("stdout update: %s", out3)
            except RuntimeError as exc:
                logging.error("Falha ao executar 'update': %s", exc)
                return False

            return True

        # 1) devices
        try:
            devices_out = run_akvcam_command(["devices"])
            logging.debug("Saída 'devices': %s", devices_out)
        except RuntimeError as exc:
            logging.error("Falha ao consultar 'devices': %s", exc)
            return False

        device_exists = "CameraFratar1" in (devices_out or "")

        if not device_exists:
            if not _create_device_sequence():
                return False
            # Verifica formato após criação
            if not _format_exists():
                logging.error("Formato esperado não encontrado após criação do device.")
                return False
            _save_last_format(desired)
            logging.info("Device criado e formato garantido com sucesso.")
            return True

        # 2) formats
        if _format_exists():
            logging.info("formato já presente; nenhuma alteração necessária.")
            _save_last_format(desired)
            return True

        logging.info("formato ausente; adicionando e atualizando.")
        try:
            out_add = run_akvcam_command(
                [
                    "add-format",
                    "CameraFratar1",
                    "YUY2",
                    str(desired["width"]),
                    str(desired["height"]),
                    str(desired["fps"]),
                ]
            )
            logging.debug("stdout add-format: %s", out_add)
            out_upd = run_akvcam_command(["update"])
            logging.debug("stdout update: %s", out_upd)
        except RuntimeError as exc:
            msg = str(exc)
            logging.warning("Falha ao adicionar formato ao device existente: %s", msg)
            # Se o device sumiu ou entrou em estado inconsistente, recupera recriando
            try:
                devs = run_akvcam_command(["devices"])
                if "CameraFratar1" in devs:
                    logging.info("Estado inconsistente detectado; removendo device e recriando.")
                    remove_virtual_camera()
                    run_akvcam_command(["update"])
                    if not _create_device_sequence():
                        return False
                else:
                    logging.info("Device não encontrado após falha; criando do zero.")
                    if not _create_device_sequence():
                        return False
            except Exception as inner:
                logging.exception("Erro ao recuperar de falha ao adicionar formato: %s", inner)
                return False

        # Verifica novamente e persiste cache somente no sucesso
        if not _format_exists():
            logging.error("Formato esperado não encontrado após 'add-format' e 'update'.")
            return False

        _save_last_format(desired)
        logging.info("Formato YUY2 %dx%d@%d garantido com sucesso.", desired["width"], desired["height"], desired["fps"])
        return True

    except Exception as exc:
        logging.exception("Erro inesperado em ensure_device_for_format: %s", exc)
        return False
