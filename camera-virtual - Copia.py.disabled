import sys
import os
import subprocess
import tempfile
import ctypes
import xml.etree.ElementTree as ET
import signal
import time
import logging
import traceback
import pystray
from PIL import Image, ImageDraw
import threading
import tkinter as tk
from tkinter import ttk
import win32event
import win32api
import winerror
import psutil
import win32gui
import win32con
from camera_virtual_fratar.streaming import (
    start_streaming as lib_start_streaming,
    stop_streaming as lib_stop_streaming,
    check_connected_clients as lib_check_connected_clients,
)


def ensure_single_instance():
    mutex_name = "Global\\VirtualCameraFratarMutex"
    handle = win32event.CreateMutex(None, False, mutex_name)
    if win32api.GetLastError() == winerror.ERROR_ALREADY_EXISTS:
        print("Uma instância do aplicativo já está em execução.")
        sys.exit(0)
    return handle

def setup_logging():
    log_dir = os.path.join(os.environ['APPDATA'], 'VirtualCameraFratar')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'camera_virtual_fratar.log')
    logging.basicConfig(filename=log_file, level=logging.DEBUG,
                        format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Adicionar logging para stdout e stderr
    sys.stdout = LoggerWriter(logging.info)
    sys.stderr = LoggerWriter(logging.error)

class LoggerWriter:
    def __init__(self, level):
        self.level = level

    def write(self, message):
        if message != '\n':
            self.level(message)

    def flush(self):
        pass

def resource_path(relative_path):
    base_path = getattr(sys, "_MEIPASS", os.path.abspath("."))
    return os.path.join(base_path, relative_path)

AKVCAM_PATH = resource_path("AkVCamManager.exe")

def log_uncaught_exceptions(exc_type, exc_value, exc_traceback):
    logging.error("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))

sys.excepthook = log_uncaught_exceptions

def run_akvcam_command(command):
    full_command = f'"{AKVCAM_PATH}" {command}'
    result = subprocess.run(full_command, capture_output=True, text=True, shell=True)
    return result.stdout.strip()

def list_devices():
    return run_akvcam_command("devices")

def list_clients():
    return run_akvcam_command("clients")

def get_dump_info():
    xml_string = run_akvcam_command("dump")
    return ET.fromstring(xml_string)

def virtual_camera_exists():
    devices = list_devices()
    return "CameraFratar1" in devices

def create_virtual_camera():
    commands = [
        'add-device -i CameraFratar1 "Virtual Camera Fratar 1"',
        'add-format CameraFratar1 YUY2 1280 720 16',
        'update'
    ]
    for cmd in commands:
        run_akvcam_command(cmd)
    logging.info("Câmera virtual criada com sucesso.")

def start_streaming():
    """
    Compat wrapper que delega para camera_virtual_fratar.streaming.start_streaming().
    Mantém a assinatura original (retorna um Popen-like) para o código existente que chama start_streaming().
    """
    try:
        return lib_start_streaming()
    except Exception as exc:
        logging.exception("Erro ao delegar start_streaming para camera_virtual_fratar.streaming: %s", exc)
        return None

def stop_process_gracefully(process):
    if process:
        try:
            # Tenta encerrar o processo graciosamente
            process.terminate()
            # Aguarda até 5 segundos para o processo encerrar
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            # Se o processo não encerrar após 5 segundos, força o encerramento
            process.kill()
        except Exception as e:
            logging.error(f"Erro ao tentar parar o streaming: {e}")
        finally:
            process = None
    return process

def check_clients():
    try:
        return lib_check_connected_clients()
    except Exception:
        logging.exception("Erro ao verificar clientes conectados via biblioteca")
        return 0

class VirtualCameraApp:
    def __init__(self):
        self.root = None
        self.icon = None
        self.is_running = False
        self.streaming_process = None
        self.status_label = None
        self.client_count = 0
        self.streaming_active = False
        self.create_tray_icon()
    
    def create_image(self):
        # Criar uma imagem simples para o ícone
        width = 64
        height = 64
        color1 = (255, 0, 0)  # Vermelho
        color2 = (255, 255, 255)  # Branco
        image = Image.new('RGB', (width, height), color1)
        dc = ImageDraw.Draw(image)
        dc.rectangle([width // 2, 0, width, height], fill=color2)
        return image

    def create_tray_icon(self):
        image = self.create_image()
        menu = pystray.Menu(
            pystray.MenuItem("Abrir", self.toggle_window),
            pystray.MenuItem("Sair", self.quit_app)
        )
        self.icon = pystray.Icon("VirtualCamera", image, "Câmera Virtual Fratar", menu)

    def run_tray_icon(self):
        run_detached = getattr(self.icon, "run_detached", None)
        if callable(run_detached):
            run_detached()
        else:
            run_method = getattr(self.icon, "run", None)
            if callable(run_method):
                threading.Thread(target=run_method, daemon=True).start()
            else:
                logging.error("pystray.Icon não possui run nem run_detached")

    def create_window(self):
        if self.root is None or not self.root.winfo_exists():
            self.root = tk.Tk()
            self.root.title("Câmera Virtual Fratar")
            self.root.geometry("300x200")
            
            frame = ttk.Frame(self.root, padding="10")
            frame.grid(row=0, column=0, sticky="nsew")
            
            ttk.Label(frame, text="Status:").grid(column=0, row=0, sticky=tk.W)
            self.status_label = ttk.Label(frame, text="Parado" if not self.is_running else "Em execução")
            self.status_label.grid(column=1, row=0, sticky=tk.W)
            
            start_button = ttk.Button(frame, text="Iniciar", command=self.start_camera)
            start_button.grid(column=0, row=1, sticky=tk.W)
            
            stop_button = ttk.Button(frame, text="Parar", command=self.stop_camera)
            stop_button.grid(column=1, row=1, sticky=tk.W)
            
            self.root.protocol("WM_DELETE_WINDOW", self.hide_window)
        self.root.deiconify()

    def toggle_window(self, icon, item):
        icon.notify("Tentando abrir a janela...")
        if self.root is None or not self.root.winfo_exists():
            self.create_window()
        elif self.root.state() == 'withdrawn':
            self.root.deiconify()
        else:
            self.root.withdraw()

    def hide_window(self):
        if self.root:
            self.root.withdraw()

    def start_camera(self):
        if not self.is_running:
            self.is_running = True
            if not hasattr(self, 'camera_thread') or not self.camera_thread.is_alive():
                self.camera_thread = threading.Thread(target=self.run_camera_virtual, daemon=True)
                self.camera_thread.start()
            self.update_status("Em execução")

    def stop_camera(self):
        if self.is_running:
            self.is_running = False
            if self.streaming_process:
                self.streaming_process = lib_stop_streaming(self.streaming_process)
            self.update_status("Parado")

    def update_status(self, status):
        if self.status_label:
            self.status_label.config(text=status)
        logging.info(f"Status da câmera: {status}")

    def run_camera_virtual(self):
        logging.info("Iniciando run_camera_virtual")
        try:
            if not virtual_camera_exists():
                create_virtual_camera()

            while self.is_running:
                try:
                    current_clients = check_clients()
                    
                    if current_clients > 0 and not self.streaming_active:
                        logging.info("Iniciando streaming...")
                        self.streaming_process = start_streaming()
                        self.streaming_active = True
                        self.update_status("Streaming ativo")
                    elif current_clients == 0 and self.streaming_active:
                        logging.info("Parando streaming...")
                        self.stop_streaming()
                    
                    self.client_count = current_clients
                    time.sleep(5)
                except Exception as e:
                    logging.error(f"Erro durante a execução do loop: {str(e)}")
                    logging.error(traceback.format_exc())
                    time.sleep(5)

            self.stop_streaming()

        except Exception as e:
            logging.error(f"Erro crítico em run_camera_virtual: {str(e)}")
            logging.error(traceback.format_exc())

        logging.info("run_camera_virtual encerrado")
        self.update_status("Câmera parada")
    def stop_streaming(self):
        if self.streaming_active:
            logging.info("Encerrando processos de streaming")
            if self.streaming_process:
                self.streaming_process = lib_stop_streaming(self.streaming_process)
            
            # Procurar e encerrar processos ffmpeg e akvcammanager
            for proc in psutil.process_iter(['name']):
                if proc.name().lower() in ['ffmpeg.exe', 'akvcammanager.exe']:
                    try:
                        proc.terminate()
                        proc.wait(timeout=5)
                    except psutil.TimeoutExpired:
                        proc.kill()
            
            self.streaming_active = False
            self.update_status("Streaming parado")
    def quit_app(self, icon, item):
        self.stop_camera()
        if self.root:
            self.root.quit()
        icon.stop()
        logging.info("Aplicação encerrada")

def is_already_running():
    current_process = psutil.Process()
    try:
        for p in psutil.process_iter(['name', 'exe']):
            try:
                info = p.as_dict(attrs=['name', 'exe'], ad_value=None)
            except Exception:
                continue
            if info.get('name') == current_process.name() and p.pid != current_process.pid:
                if info.get('exe') == current_process.exe():
                    return True
    except Exception:
        logging.debug("Falha ao verificar instância já em execução", exc_info=True)
    return False

def stop_streaming(process):
    if process:
        try:
            # Envia CTRL_BREAK_EVENT para o grupo de processos
            process.send_signal(signal.CTRL_BREAK_EVENT)
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        except Exception as e:
            logging.error(f"Erro ao tentar parar o streaming: {e}")
        finally:
            process = None
    return process

def main():
    mutex_handle = ensure_single_instance()
    
    setup_logging()
    logging.info("Iniciando aplicação")
    
    try:
        app = VirtualCameraApp()
        app.start_camera()
        logging.info("Câmera iniciada")
        app.run_tray_icon()
    except Exception as e:
        logging.error(f"Erro crítico na função main: {e}")
        logging.error(traceback.format_exc())
    finally:
        logging.info("Aplicação encerrada")
        win32api.CloseHandle(mutex_handle)

if __name__ == '__main__':
    main()