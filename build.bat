@echo off
REM Verifica se o PyInstaller está instalado
pyinstaller --version >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo PyInstaller não está instalado. Instalando agora...
    pip install pyinstaller
)

REM Verifica arquivos necessários
echo Verificando arquivos necessários...

set MISSING_FILES=0
if not exist "ffmpeg.exe" (
    echo ERRO: ffmpeg.exe não encontrado
    set MISSING_FILES=1
)
if not exist "ffprobe.exe" (
    echo ERRO: ffprobe.exe não encontrado
    set MISSING_FILES=1
)
rem if not exist "AkVCamManager.exe" (
rem     echo ERRO: AkVCamManager.exe não encontrado
rem     set MISSING_FILES=1
rem )
rem if not exist "AkVirtualCamera.dll" (
rem     echo ERRO: AkVirtualCamera.dll não encontrado
rem     set MISSING_FILES=1
rem )
if not exist "akvirtualcamera-windows-9.1.2.exe" (
    echo ERRO: akvirtualcamera-windows-9.1.2.exe não encontrado
    set MISSING_FILES=1
)

if %MISSING_FILES%==1 (
    echo.
    echo Por favor, coloque todos os arquivos necessários no diretório antes de continuar.
    pause
    exit /b 1
)

REM Mostra tamanho dos arquivos de entrada
echo.
echo Tamanho dos arquivos de entrada:
dir ffmpeg.exe ffprobe.exe akvirtualcamera-windows-9.1.2.exe

REM Limpa builds anteriores para garantir reprodutibilidade
echo.
echo Limpando diretórios de build/dist e removendo spec antigo (se presentes)...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist virtual_camera_fratar.spec del /q virtual_camera_fratar.spec
if exist camera-virtual-fratar.spec del /q camera-virtual-fratar.spec

REM Compila o código Python em um executável sem console
echo.
echo Compilando o executável (PyInstaller --clean --noconfirm)...
REM Ajustado para fluxo 100% offline: inclui binários adicionais conforme seção
REM "Recomendações de build para 100% offline" em [Instalação.md](Instalação.md:81).
REM Habilita console para que --help e logs sejam exibidos no terminal
pyinstaller --clean --noconfirm --name virtual_camera_fratar ^
           --onefile ^
           --console ^
           --add-binary "%CD%\ffmpeg.exe;." ^
           --add-binary "%CD%\ffprobe.exe;." ^
           --add-binary "%CD%\akvirtualcamera-windows-9.1.2.exe;." ^
           camera-virtual.py

REM Verifica se PyInstaller teve sucesso
if %ERRORLEVEL% NEQ 0 (
    echo Erro: PyInstaller falhou.
    pause
    exit /b %ERRORLEVEL%
)

REM Move o executável para o diretório atual
if exist dist\virtual_camera_fratar.exe move /Y dist\virtual_camera_fratar.exe .

REM Verifica o resultado
echo.
echo Tamanho do executável gerado:
if exist virtual_camera_fratar.exe dir virtual_camera_fratar.exe

echo.
echo Verificando conteúdo do executável com strings...
REM (opcional) você pode adicionar strings.exe ou outra ferramenta aqui

REM Limpa os arquivos temporários gerados pelo PyInstaller mantendo o exe
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist virtual_camera_fratar.spec del /q virtual_camera_fratar.spec

echo.
echo Compilação concluída com sucesso.
pause