import logging
import os
import shutil
import signal
import subprocess
import threading
from dataclasses import dataclass
from typing import Optional
 
# Os imports abaixo precisam funcionar tanto quando o pacote é executado via
# `python -m camera_virtual_fratar.streaming` quanto quando o arquivo é rodado
# diretamente (`python camera_virtual_fratar/streaming.py`). Em execução direta,
# imports relativos falham; por isso primeiro tentamos o caminho absoluto e
# fazemos fallback para o import local.
try:  # pragma: no cover - execução depende do contexto de importação
    from camera_virtual_fratar.virtual_camera import (
        check_clients,
        resource_path,
        ensure_device_for_format,
        virtual_camera_exists,
    )
except ImportError:  # pragma: no cover - usado quando rodado como script
    from .virtual_camera import (  # type: ignore[no-redef]
        check_clients,
        resource_path,
        ensure_device_for_format,
        virtual_camera_exists,
    )
# Compat wrapper: manter função start_streaming() disponível para código antigo.
# Ela extrai a URL do FFMPEG_CMD definido abaixo e chama start_streaming_for_url(url).


FFMPEG_CMD = [
    "-reconnect",
    "1",
    "-reconnect_streamed",
    "1",
    "-reconnect_at_eof",
    "1",
    "-i",
    "http://camera:<EMAIL>:1081/h264/salareuniao/temp.h264",
    "-f",
    "rawvideo",
    "-pix_fmt",
    "yuyv422",
    "-video_size",
    "1280x720",
    "-r",
    "16",
    "-fflags",
    "nobuffer",
    "-",
]

AKVCAM_ARGS = [
    "stream",
    "CameraFratar1",
    "YUY2",
    "1280",
    "720",
]

CREATE_FLAGS = subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NO_WINDOW


@dataclass
class StreamingProcess:
    ffmpeg: subprocess.Popen
    akvcam: subprocess.Popen


def _start_stderr_logger(process: subprocess.Popen, label: str) -> None:
    stream = getattr(process, "stderr", None)
    if not stream:
        return

    def _reader() -> None:
        try:
            for raw_line in iter(stream.readline, b""):
                if not raw_line:
                    continue
                try:
                    line = raw_line.decode("utf-8", errors="ignore").strip()
                except Exception:
                    line = repr(raw_line)
                if line:
                    logging.debug("%s stderr: %s", label, line)
        except Exception:
            logging.debug("Falha ao ler stderr de %s", label, exc_info=True)
        finally:
            try:
                stream.close()
            except Exception:
                pass

    threading.Thread(target=_reader, name=f"{label}-stderr", daemon=True).start()


def _create_startupinfo() -> subprocess.STARTUPINFO:
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE
    return startupinfo


def start_streaming_for_url(url: str) -> Optional[StreamingProcess]:
    """
    Inicia pipeline FFmpeg -> AkVCam a partir de uma URL, mas:
    - Proba a URL (width,height,fps,pix_fmt) antes de qualquer criação de device
    - Se o formato difere do atualmente configurado, recria o device via ensure_device_for_format()
    - Somente após sucesso do probing e (se necessário) recriação, inicia o ffmpeg e o AkVCamManager stream
    Retorna StreamingProcess em caso de sucesso, ou None em caso de falha (não inicia streaming).
    """
    try:  # pragma: no cover - depende de como o módulo foi carregado
        from camera_virtual_fratar.video_probe import probe_url
    except ImportError:  # pragma: no cover - fallback para execução direta
        from .video_probe import probe_url  # type: ignore[no-redef]

    logging.info("Iniciando probing da URL antes de criar/iniciar streaming: %s", url)
    probe = probe_url(url)

    # Resolver caminhos dos binários via resource_path antes de spawn
    ffmpeg_path = resource_path("ffmpeg.exe")
    ffprobe_path = resource_path("ffprobe.exe")
    akvcam_path = resource_path("AkVCamManager.exe")

    # Logar caminhos resolvidos e existência
    ffmpeg_exists = os.path.exists(ffmpeg_path) if ffmpeg_path else False
    ffprobe_exists = os.path.exists(ffprobe_path) if ffprobe_path else False
    akvcam_exists = os.path.exists(akvcam_path) if akvcam_path else False
    ffmpeg_which = shutil.which("ffmpeg.exe") or shutil.which("ffmpeg")
    ffprobe_which = shutil.which("ffprobe.exe") or shutil.which("ffprobe")

    if not ffmpeg_exists and ffmpeg_which:
        logging.warning(
            "ffmpeg.exe não encontrado em %s; usando binário do PATH: %s",
            ffmpeg_path,
            ffmpeg_which,
        )
        ffmpeg_path = ffmpeg_which
        ffmpeg_exists = True

    if not akvcam_exists:
        logging.error("AkVCamManager.exe não encontrado em %s", akvcam_path)

    logging.info(
        "Resolved binaries: ffmpeg=%s (exists=%s, which=%s), ffprobe=%s (exists=%s, which=%s), AkVCamManager=%s (exists=%s)",
        ffmpeg_path,
        ffmpeg_exists,
        ffmpeg_which,
        ffprobe_path,
        ffprobe_exists,
        ffprobe_which,
        akvcam_path,
        akvcam_exists,
    )

    if not ffmpeg_exists:
        logging.error("Nenhum ffmpeg válido encontrado; abortando start_streaming_for_url")
        return None

    if not akvcam_exists:
        logging.error("Nenhum AkVCamManager válido encontrado; abortando start_streaming_for_url")
        return None

    if not probe:
        logging.warning(
            "Probing falhou para URL %s. Seguindo com defaults compatíveis (1280x720@16, pix_fmt=yuyv422).",
            url,
        )
        src_width = 1280
        src_height = 720
        src_fps = 16.0
        pix_fmt = "yuyv422"
    else:
        try:
            src_width = int(probe["width"])
            src_height = int(probe["height"])
            src_fps = float(probe["fps"])
            pix_fmt = probe.get("pix_fmt")
            logging.info(
                "URL probed -> %dx%d@%.3g (pix_fmt=%s)",
                src_width,
                src_height,
                src_fps,
                pix_fmt,
            )
        except Exception:
            logging.warning(
                "Probe retornou valores inválidos para URL %s (%s). Usando defaults 1280x720@16 yuyv422.",
                url,
                probe,
            )
            src_width = 1280
            src_height = 720
            src_fps = 16.0
            pix_fmt = "yuyv422"

    # A pipeline original sempre entrega YUY2 1280x720 @ 16fps para o driver.
    target_width = 1280
    target_height = 720
    target_fps = 16

    if (src_width, src_height, src_fps) != (target_width, target_height, target_fps):
        logging.info(
            "Forçando saída compatível com o driver: entrada %dx%d@%d -> saída %dx%d@%d YUY2",
            src_width,
            src_height,
            src_fps,
            target_width,
            target_height,
            target_fps,
        )

    # Garantir que o device exista com o formato desejado (YUY2)
    ok = ensure_device_for_format(width=target_width, height=target_height, fps=target_fps)
    if not ok:
        if virtual_camera_exists():
            logging.warning(
                "Falha ao garantir device %dx%d@%d via ensure_device_for_format, mas dispositivo existe."
                " Prosseguindo com tentativa de iniciar streaming.",
                target_width,
                target_height,
                target_fps,
            )
        else:
            logging.error(
                "Falha ao garantir device com formato %dx%d@%d e dispositivo ausente. Abortando streaming.",
                target_width,
                target_height,
                target_fps,
            )
            return None

    ffmpeg_proc: Optional[subprocess.Popen] = None
    akvcam_proc: Optional[subprocess.Popen] = None
    started_successfully = False

    try:
        # Construir comando ffmpeg com consistência: forçar saída YUY2 (yuyv422) e dimensões/fps
        ffmpeg_cmd = [
            ffmpeg_path,
            "-re",
            "-reconnect",
            "1",
            "-reconnect_streamed",
            "1",
            "-reconnect_at_eof",
            "1",
            "-i",
            url,
        ]

        filters = []
        if (src_width, src_height) != (target_width, target_height):
            filters.append(f"scale={target_width}:{target_height}")
        if int(round(src_fps)) != target_fps:
            filters.append(f"fps={target_fps}")

        if filters:
            ffmpeg_cmd.extend(["-vf", ",".join(filters)])

        ffmpeg_cmd.extend(
            [
                "-f",
                "rawvideo",
                "-pix_fmt",
                "yuyv422",
                "-video_size",
                f"{target_width}x{target_height}",
                "-r",
                str(target_fps),
                "-fflags",
                "nobuffer",
                "-",
            ]
        )

        # AkVCamManager stream espera os parâmetros: device, formato, largura, altura, fps
        akvcam_args = [
            akvcam_path,
            "stream",
            "CameraFratar1",
            "YUY2",
            str(target_width),
            str(target_height),
            str(target_fps),
        ]

        logging.debug("Launching ffmpeg: %s", " ".join(ffmpeg_cmd))
        ffmpeg_proc = subprocess.Popen(
            ffmpeg_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=CREATE_FLAGS,
            startupinfo=_create_startupinfo(),
            text=False,
            bufsize=0,
        )

        # Checar se ffmpeg morreu imediatamente
        if ffmpeg_proc.poll() is not None:
            err_tail = ""
            if ffmpeg_proc.stderr:
                try:
                    err_data = ffmpeg_proc.stderr.read()
                    if err_data:
                        err_tail = err_data.decode("utf-8", errors="ignore")[-1000:]
                except Exception:
                    err_tail = "<erro ao ler stderr do ffmpeg>"
            logging.error(
                "ffmpeg terminou imediatamente com código %s. stderr (tail): %s. cmd: %s",
                ffmpeg_proc.returncode,
                err_tail,
                " ".join(ffmpeg_cmd),
            )
            return None

        _start_stderr_logger(ffmpeg_proc, "ffmpeg")

        logging.debug("Launching AkVCamManager stream: %s", " ".join(akvcam_args))
        akvcam_proc = subprocess.Popen(
            akvcam_args,
            stdin=ffmpeg_proc.stdout,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.PIPE,
            creationflags=CREATE_FLAGS,
            startupinfo=_create_startupinfo(),
            text=False,
            bufsize=0,
        )

        # Fechar stdout do ffmpeg no pai para permitir que akvcam leia diretamente
        if ffmpeg_proc.stdout:
            ffmpeg_proc.stdout.close()

        # Checar se AkVCamManager morreu imediatamente
        if akvcam_proc.poll() is not None:
            ak_err_tail = ""
            if akvcam_proc.stderr:
                try:
                    ak_err_data = akvcam_proc.stderr.read()
                    if ak_err_data:
                        ak_err_tail = ak_err_data.decode("utf-8", errors="ignore")[-1000:]
                except Exception:
                    ak_err_tail = "<erro ao ler stderr do AkVCamManager>"

            # Encerrar ffmpeg caso esteja vivo
            try:
                if ffmpeg_proc.poll() is None:
                    try:
                        ffmpeg_proc.kill()
                    except Exception:
                        pass
            except Exception:
                pass

            logging.error(
                "AkVCamManager terminou imediatamente com código %s. stderr (tail): %s. cmd: %s",
                akvcam_proc.returncode,
                ak_err_tail,
                " ".join(akvcam_args),
            )
            return None

        _start_stderr_logger(akvcam_proc, "AkVCamManager")

        started_successfully = True
        logging.info(
            "Pipeline FFmpeg -> AkVCam iniciado (PIDs: %s, %s) para %dx%d@%d",
            ffmpeg_proc.pid,
            akvcam_proc.pid,
            target_width,
            target_height,
            target_fps,
        )
        return StreamingProcess(ffmpeg=ffmpeg_proc, akvcam=akvcam_proc)
    except FileNotFoundError as exc:
        logging.error(
            "Arquivo necessário não encontrado para iniciar streaming: %s. Comandos tentados: ffmpeg=%s, akvcam=%s",
            exc,
            " ".join(ffmpeg_cmd) if 'ffmpeg_cmd' in locals() else "<n/a>",
            " ".join(akvcam_args) if 'akvcam_args' in locals() else "<n/a>",
        )
    except Exception as exc:
        logging.exception("Falha ao iniciar pipeline de streaming: %s", exc)
    finally:
        if not started_successfully:
            for proc in (akvcam_proc, ffmpeg_proc):
                if proc is None:
                    continue
                try:
                    if proc.poll() is None:
                        _send_break(proc)
                        _wait_and_cleanup(proc)
                except Exception:
                    try:
                        proc.kill()
                    except Exception:
                        pass
    return None


def start_streaming() -> Optional[StreamingProcess]:
    """
    Compat wrapper para código legado: obtém a URL da configuração FFMPEG_CMD e delega para start_streaming_for_url(url).
    """
    # Encontrar o parâmetro após '-i' em FFMPEG_CMD
    url: Optional[str] = None
    try:
        for i, tok in enumerate(FFMPEG_CMD):
            if tok == "-i" and i + 1 < len(FFMPEG_CMD):
                candidate = FFMPEG_CMD[i + 1]
                # Ignorar caso patológico em que o próximo token é outro flag
                if candidate.startswith("-"):
                    continue
                url = candidate
                break
    except Exception:
        logging.debug("Falha ao extrair URL de FFMPEG_CMD", exc_info=True)
        url = None

    if not url:
        logging.error("Não foi possível determinar a URL de entrada a partir do FFMPEG_CMD; abortando start_streaming()")
        return None

    logging.info("start_streaming(): iniciando pipeline para URL extraída do FFMPEG_CMD: %s", url)
    return start_streaming_for_url(url)


def _send_break(process: subprocess.Popen) -> None:
    try:
        process.send_signal(signal.CTRL_BREAK_EVENT)
    except Exception:
        process.terminate()


def _wait_and_cleanup(process: subprocess.Popen) -> None:
    try:
        process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        process.kill()
    except Exception:
        process.kill()


def stop_streaming(process: Optional[StreamingProcess]) -> Optional[StreamingProcess]:
    if process is None:
        return None

    for proc in (process.akvcam, process.ffmpeg):
        if proc is None:
            continue
        if proc.poll() is not None:
            continue
        _send_break(proc)

    for proc in (process.akvcam, process.ffmpeg):
        if proc is None:
            continue
        _wait_and_cleanup(proc)

    logging.info("Pipeline FFmpeg -> AkVCam finalizado")
    return None


def check_connected_clients() -> int:
    clients = check_clients()
    logging.debug("check_connected_clients -> %s", clients)
    return clients
