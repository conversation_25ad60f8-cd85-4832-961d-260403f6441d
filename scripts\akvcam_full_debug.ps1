# AkVirtualCamera end-to-end debug collector
[CmdletBinding()]
param(
  [string]$DeviceId = "CameraFratar1",
  [string]$Format = "YUY2",
  [int]$Width = 1280,
  [int]$Height = 720,
  [int]$Fps = 16,
  [switch]$TryStartService = $false
)

Set-StrictMode -Version Latest
$ErrorActionPreference = "Continue"

function Ensure-Admin {
  $id = [Security.Principal.WindowsIdentity]::GetCurrent()
  $p = New-Object Security.Principal.WindowsPrincipal($id)
  if (-not $p.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "Elevando privilégios..." -ForegroundColor Yellow
    $psi = @{
      FilePath = "$PSHOME\pwsh.exe"
      ArgumentList = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`""
      Verb = "RunAs"
      WindowStyle = "Normal"
    }
    Start-Process @psi
    exit
  }
}
Ensure-Admin

try { Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass -Force } catch {}

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$OutDir = Join-Path $env:TEMP "akvcam_debug_$timestamp"
New-Item -ItemType Directory -Path $OutDir -Force | Out-Null
Start-Transcript -Path (Join-Path $OutDir "transcript.txt") -Force | Out-Null

Write-Host "Saída agregada em: $OutDir" -ForegroundColor Cyan

# Localizar AkVCamManager instalado (x64 preferido)
$M = $null
$cands = @(
  "C:\Program Files\AkVirtualCamera\x64\AkVCamManager.exe",
  "C:\Program Files\AkVirtualCamera\AkVCamManager.exe"
)
$found = Get-ChildItem -Recurse -ErrorAction SilentlyContinue "C:\Program Files*", "C:\Program Files (x86)*" -Filter AkVCamManager.exe | Select-Object -ExpandProperty FullName
$cands = $cands + $found
foreach ($c in $cands | Get-Unique) {
  if (Test-Path $c) { $M = $c; break }
}
if (-not $M) {
  "AkVCamManager.exe não encontrado." | Tee-Object -FilePath (Join-Path $OutDir "error.txt")
  Stop-Transcript | Out-Null
  exit 1
}
$MDir = Split-Path $M
Set-Location $MDir
$Assistant = Join-Path $MDir "AkVCamAssistant.exe"

"Usando Manager: $M" | Tee-Object -FilePath (Join-Path $OutDir "manager_path.txt")
if (-not (Test-Path $Assistant)) {
  "Aviso: AkVCamAssistant.exe não encontrado em $MDir (isso pode impedir o launchService)." | Tee-Object -Append -FilePath (Join-Path $OutDir "manager_path.txt")
}

# Parte 1 — Básico
& $M --version 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "01_version.txt")
& $M supported-formats --output 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "02_supported_formats.txt")
& $M devices 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "03_devices_before.txt")
& $M set-loglevel 7 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "04_set_loglevel.txt")

# Parte 2 — Serviços (somente listar por padrão)
Get-Service | Where-Object { $_.DisplayName -match "Ak|Virtual|Camera" -or $_.Name -match "Ak|Virtual|Camera" } |
  Select-Object Name,DisplayName,Status |
  Tee-Object -FilePath (Join-Path $OutDir "05_services_list.txt") | Out-Null

if ($TryStartService) {
  $svcList = Get-Content (Join-Path $OutDir "05_services_list.txt") -ErrorAction SilentlyContinue
  $candidates = Get-Service | Where-Object { $_.Name -match "AkVCam|AkVirtual|Webcamoid" -or $_.DisplayName -match "AkVCam|AkVirtual|Webcamoid" }
  foreach ($svc in $candidates) {
    if ($svc.Status -eq 'Stopped') {
      "Tentando iniciar serviço: $($svc.Name) ($($svc.DisplayName))" | Tee-Object -Append -FilePath (Join-Path $OutDir "06_service_start.txt")
      try { Start-Service -Name $svc.Name -ErrorAction Stop; "Start OK" | Tee-Object -Append -FilePath (Join-Path $OutDir "06_service_start.txt") }
      catch { ($_ | Out-String) | Tee-Object -Append -FilePath (Join-Path $OutDir "06_service_start.txt") }
    }
  }
}

# Parte 3 — Criação direta
& $M add-device -i $DeviceId "Virtual Camera Fratar 1" 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "07_add_device.txt")
& $M add-format $DeviceId $Format $Width $Height $Fps 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "08_add_format.txt")
& $M update 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "09_update.txt")
& $M devices 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "10_devices_after.txt")
& $M formats $DeviceId 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "11_formats.txt")

# Checar se device não existe e tentar INI
$needIni = Select-String -Path (Join-Path $OutDir "07_add_device.txt") -Pattern "Failed to create device|error|failed" -SimpleMatch -Quiet
$fmtErr = Select-String -Path (Join-Path $OutDir "11_formats.txt") -Pattern "doesn't exists" -SimpleMatch -Quiet
if ($needIni -or $fmtErr) {
  $iniPath = Join-Path $OutDir "akvcam_settings.ini"
  $ini = @"
[Cameras]
cameras/size = 1
cameras/1/description = Virtual Camera Fratar
cameras/1/formats = 1
cameras/1/id = $DeviceId

[Formats]
formats/size = 1
formats/1/format = $Format
formats/1/width = $Width
formats/1/height = $Height
formats/1/fps = $Fps
"@
  Set-Content -Path $iniPath -Value $ini -Encoding ASCII
  & $M load $iniPath 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "12_load_ini.txt")
  & $M devices 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "13_devices_after_ini.txt")
  & $M formats $DeviceId 2>&1 | Tee-Object -FilePath (Join-Path $OutDir "14_formats_after_ini.txt")
}

# Parte 5 — Logs internos
$akLogs = Get-ChildItem "$env:LOCALAPPDATA\Temp\AkVirtualCamera-*.log" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending
if ($akLogs) {
  Get-Content $akLogs[0].FullName -Tail 200 | Tee-Object -FilePath (Join-Path $OutDir "15_AkVirtualCamera_tail.txt") | Out-Null
} else {
  "Nenhum AkVirtualCamera-*.log encontrado." | Tee-Object -FilePath (Join-Path $OutDir "15_AkVirtualCamera_tail.txt")
}
$mgrLog = Join-Path $env:LOCALAPPDATA "Temp\AkVCamManager.log"
if (Test-Path $mgrLog) {
  Get-Content $mgrLog -Tail 200 | Tee-Object -FilePath (Join-Path $OutDir "16_AkVCamManager_tail.txt") | Out-Null
} else {
  "AkVCamManager.log não encontrado em $mgrLog" | Tee-Object -FilePath (Join-Path $OutDir "16_AkVCamManager_tail.txt")
}

# Processos potencialmente conflitantes
Get-Process | Where-Object { $_.Name -match "Zoom|Teams|obs64|obs32|ManyCam|YouCam|Webex|Skype|akvcam|camera" } |
  Select-Object Name,Id,MainWindowTitle |
  Tee-Object -FilePath (Join-Path $OutDir "17_processes.txt") | Out-Null

"Concluído. Cole o conteúdo de $OutDir ou compacte-o e anexe." | Tee-Object -FilePath (Join-Path $OutDir "SUMMARY.txt")

Stop-Transcript | Out-Null